2025-07-23 00:24:55.500 W [MediaServer.exe] [12572-stamp thread] util.cpp:413 operator () | Stamp expired is abnormal: ***********
2025-07-23 15:42:59.316 I [MediaServer.exe] [16984-16024] main.cpp:270 start_main | ZLMediaKit(git hash:fb2a3f5/2025-07-02T14:58:12+08:00,branch:,build time:2025-07-02T07:05:40)
2025-07-23 15:42:59.672 D [MediaServer.exe] [16984-16024] SSLBox.cpp:181 setContext | Add certificate of: default.zlmediakit.com
2025-07-23 15:42:59.672 D [MediaServer.exe] [16984-stamp thread] util.cpp:391 operator () | Stamp thread started
2025-07-23 15:42:59.676 I [MediaServer.exe] [16984-16024] EventPoller.cpp:616 EventPollerPool | EventPoller created size: 24
2025-07-23 15:42:59.676 I [MediaServer.exe] [16984-16024] main.cpp:395 start_main | 已启动http api 接口
2025-07-23 15:42:59.676 I [MediaServer.exe] [16984-16024] main.cpp:397 start_main | 已启动http hook 接口
2025-07-23 15:42:59.677 I [MediaServer.exe] [16984-16024] TcpServer.cpp:242 start_l | TCP server listening on [::]: 554
2025-07-23 15:42:59.678 I [MediaServer.exe] [16984-16024] TcpServer.cpp:242 start_l | TCP server listening on [::]: 1935
2025-07-23 15:42:59.678 I [MediaServer.exe] [16984-16024] TcpServer.cpp:242 start_l | TCP server listening on [::]: 80
2025-07-23 15:42:59.678 I [MediaServer.exe] [16984-16024] TcpServer.cpp:242 start_l | TCP server listening on [::]: 443
2025-07-23 15:42:59.680 I [MediaServer.exe] [16984-16024] UdpServer.cpp:128 start_l | UDP server bind to [::]: 10000
2025-07-23 15:42:59.680 I [MediaServer.exe] [16984-16024] TcpServer.cpp:242 start_l | TCP server listening on [::]: 10000
2025-07-23 15:42:59.681 I [MediaServer.exe] [16984-16024] UdpServer.cpp:128 start_l | UDP server bind to [::]: 8000
2025-07-23 15:42:59.681 I [MediaServer.exe] [16984-16024] TcpServer.cpp:242 start_l | TCP server listening on [::]: 8000
2025-07-23 15:42:59.682 I [MediaServer.exe] [16984-16024] UdpServer.cpp:128 start_l | UDP server bind to [::]: 9000
