name: Android
on: [push, pull_request]
jobs:
  build:
    runs-on: ubuntu-24.04
    steps:

    - name: 下载源码
      uses: actions/checkout@v1

    - name: 配置JDK
      uses: actions/setup-java@v3
      with:
        java-version: '11'
        distribution: 'temurin'
        cache: gradle

    - name: 下载submodule源码
      run: mv -f .gitmodules_github .gitmodules && git submodule sync && git submodule update --init

    - name: 赋予gradlew文件可执行权限
      run: chmod +x ./Android/gradlew

    - name: 编译
      run: cd Android && ./gradlew build

    - name: 设置环境变量
      run: |
        echo "BRANCH=$(echo ${GITHUB_REF#refs/heads/} | tr -s "/\?%*:|\"<>" "_")" >> $GITHUB_ENV  
        echo "BRANCH2=$(echo ${GITHUB_REF#refs/heads/} )" >> $GITHUB_ENV    
        echo "DATE=$(date +%Y-%m-%d)" >> $GITHUB_ENV

    - name: 打包二进制
      id: upload
      uses: actions/upload-artifact@v4
      with:
        name: ${{ github.workflow }}_${{ env.BRANCH }}_${{ env.DATE }}
        path: Android/app/build/outputs/apk/debug/*
        if-no-files-found: error
        retention-days: 90

    - name: issue评论
      if: github.event_name != 'pull_request' && github.ref != 'refs/heads/feature/test'
      uses: actions/github-script@v7
      with:
        github-token: ${{ secrets.GITHUB_TOKEN }}
        script: |
          github.rest.issues.createComment({
            issue_number: ${{vars.VERSION_ISSUE_NO}},
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: '- 下载地址: [${{ github.workflow }}_${{ env.BRANCH }}_${{ env.DATE }}](${{ steps.upload.outputs.artifact-url }})\n'
              + '- 分支: ${{ env.BRANCH2 }}\n' 
              + '- git hash: ${{ github.sha }} \n' 
              + '- 编译日期: ${{ env.DATE }}\n' 
              + '- 编译记录: [${{ github.run_id }}](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }})\n'
              + '- 开启特性: 未开启openssl/webrtc/datachannel等功能\n'
              + '- 打包ci名: ${{ github.workflow }}\n' 
          })
