#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API端点测试
采用TDD方式：先写测试，再实现功能
"""

import pytest
import json
from unittest.mock import patch, Mock
from flask import Flask


class TestAPIEndpoints:
    """API端点测试类"""
    
    def test_start_api_success(self, client, sample_video_file):
        """测试启动推流API成功"""
        # Arrange
        with patch('stream_controller.LocalFFmpegController.start_push') as mock_start:
            mock_start.return_value = {'code': 0, 'msg': '推流启动成功'}
            
            data = {
                'path': 'test_video.mp4',
                'rtmp_url': 'rtmp://test.example.com/live/stream'
            }
            
            # Act
            response = client.post('/api/start', 
                                 data=json.dumps(data),
                                 content_type='application/json')
            
            # Assert
            assert response.status_code == 200
            result = json.loads(response.data)
            assert result['code'] == 0
            assert '成功' in result['msg']
    
    def test_start_api_missing_path(self, client):
        """测试启动推流API缺少路径参数"""
        # Arrange
        data = {
            'rtmp_url': 'rtmp://test.example.com/live/stream'
            # 缺少 'path' 参数
        }
        
        # Act
        response = client.post('/api/start',
                             data=json.dumps(data),
                             content_type='application/json')
        
        # Assert
        assert response.status_code == 400
        result = json.loads(response.data)
        assert result['code'] == -1
        assert '路径' in result['msg'] or 'path' in result['msg'].lower()
    
    def test_start_api_invalid_json(self, client):
        """测试启动推流API无效JSON"""
        # Arrange
        invalid_json = "{'invalid': json}"
        
        # Act
        response = client.post('/api/start',
                             data=invalid_json,
                             content_type='application/json')
        
        # Assert
        assert response.status_code == 400
        result = json.loads(response.data)
        assert result['code'] == -1
        assert 'JSON' in result['msg'] or 'json' in result['msg']
    
    def test_stop_api_success(self, client):
        """测试停止推流API成功"""
        # Arrange
        with patch('stream_controller.LocalFFmpegController.stop_push') as mock_stop:
            mock_stop.return_value = {'code': 0, 'msg': '推流已停止'}
            
            # Act
            response = client.post('/api/stop')
            
            # Assert
            assert response.status_code == 200
            result = json.loads(response.data)
            assert result['code'] == 0
            assert '停止' in result['msg']
    
    def test_status_api_running(self, client):
        """测试状态API - 推流运行中"""
        # Arrange
        with patch('stream_controller.LocalFFmpegController.get_status') as mock_status:
            mock_status.return_value = {
                'code': 0,
                'is_playing': True,
                'current_file': 'test_video.mp4',
                'pid': 12345,
                'uptime': 120
            }
            
            # Act
            response = client.get('/api/status')
            
            # Assert
            assert response.status_code == 200
            result = json.loads(response.data)
            assert result['code'] == 0
            assert result['is_playing'] is True
            assert result['current_file'] == 'test_video.mp4'
            assert result['pid'] == 12345
    
    def test_status_api_not_running(self, client):
        """测试状态API - 推流未运行"""
        # Arrange
        with patch('stream_controller.LocalFFmpegController.get_status') as mock_status:
            mock_status.return_value = {
                'code': 0,
                'is_playing': False,
                'current_file': None,
                'pid': None
            }
            
            # Act
            response = client.get('/api/status')
            
            # Assert
            assert response.status_code == 200
            result = json.loads(response.data)
            assert result['code'] == 0
            assert result['is_playing'] is False
            assert result['current_file'] is None
    
    def test_files_api_success(self, client):
        """测试获取文件列表API成功"""
        # Arrange
        with patch('stream_controller.LocalFFmpegController.get_video_files') as mock_files:
            mock_files.return_value = {
                'code': 0,
                'files': [
                    {'name': 'video1.mp4', 'size': 1024000},
                    {'name': 'video2.avi', 'size': 2048000}
                ]
            }
            
            # Act
            response = client.get('/api/files')
            
            # Assert
            assert response.status_code == 200
            result = json.loads(response.data)
            assert result['code'] == 0
            assert len(result['files']) == 2
            assert result['files'][0]['name'] == 'video1.mp4'
    
    def test_switch_api_success(self, client):
        """测试切换视频API成功"""
        # Arrange
        with patch('stream_controller.LocalFFmpegController.switch_video') as mock_switch:
            mock_switch.return_value = {'code': 0, 'msg': '切换成功'}
            
            data = {'path': 'new_video.mp4'}
            
            # Act
            response = client.post('/api/switch',
                                 data=json.dumps(data),
                                 content_type='application/json')
            
            # Assert
            assert response.status_code == 200
            result = json.loads(response.data)
            assert result['code'] == 0
            assert '成功' in result['msg']
    
    def test_queue_add_api_success(self, client):
        """测试添加队列API成功"""
        # Arrange
        with patch('stream_controller.LocalFFmpegController.add_to_queue') as mock_add:
            mock_add.return_value = {'code': 0, 'msg': '已添加到队列'}
            
            data = {'path': 'queue_video.mp4'}
            
            # Act
            response = client.post('/api/queue/add',
                                 data=json.dumps(data),
                                 content_type='application/json')
            
            # Assert
            assert response.status_code == 200
            result = json.loads(response.data)
            assert result['code'] == 0
            assert '队列' in result['msg']
    
    def test_queue_list_api_success(self, client):
        """测试获取队列列表API成功"""
        # Arrange
        with patch('stream_controller.LocalFFmpegController.get_queue') as mock_queue:
            mock_queue.return_value = {
                'code': 0,
                'queue': ['video1.mp4', 'video2.mp4'],
                'current_index': 0
            }
            
            # Act
            response = client.get('/api/queue')
            
            # Assert
            assert response.status_code == 200
            result = json.loads(response.data)
            assert result['code'] == 0
            assert len(result['queue']) == 2
            assert result['current_index'] == 0
    
    def test_queue_clear_api_success(self, client):
        """测试清空队列API成功"""
        # Arrange
        with patch('stream_controller.LocalFFmpegController.clear_queue') as mock_clear:
            mock_clear.return_value = {'code': 0, 'msg': '队列已清空'}
            
            # Act
            response = client.post('/api/queue/clear')
            
            # Assert
            assert response.status_code == 200
            result = json.loads(response.data)
            assert result['code'] == 0
            assert '清空' in result['msg']


class TestAPIAuthentication:
    """API认证测试"""
    
    def test_api_without_auth_header(self, client):
        """测试没有认证头的API请求"""
        # 如果实现了API认证，这个测试应该失败
        # Arrange
        data = {'path': 'test_video.mp4'}
        
        # Act
        response = client.post('/api/start',
                             data=json.dumps(data),
                             content_type='application/json')
        
        # Assert
        # 如果没有实现认证，应该返回200
        # 如果实现了认证，应该返回401
        assert response.status_code in [200, 401]
    
    def test_api_with_invalid_auth_header(self, client):
        """测试无效认证头的API请求"""
        # Arrange
        data = {'path': 'test_video.mp4'}
        headers = {'X-API-Key': 'invalid_key'}
        
        # Act
        response = client.post('/api/start',
                             data=json.dumps(data),
                             content_type='application/json',
                             headers=headers)
        
        # Assert
        # 如果实现了认证，应该返回401
        # 如果没有实现认证，应该返回200
        assert response.status_code in [200, 401]
    
    def test_api_with_valid_auth_header(self, client):
        """测试有效认证头的API请求"""
        # Arrange
        with patch('stream_controller.LocalFFmpegController.start_push') as mock_start:
            mock_start.return_value = {'code': 0, 'msg': '推流启动成功'}
            
            data = {'path': 'test_video.mp4'}
            headers = {'X-API-Key': 'valid_key'}  # 需要配置有效的密钥
            
            # Act
            response = client.post('/api/start',
                                 data=json.dumps(data),
                                 content_type='application/json',
                                 headers=headers)
            
            # Assert
            assert response.status_code == 200


class TestAPIErrorHandling:
    """API错误处理测试"""
    
    def test_api_internal_error(self, client):
        """测试API内部错误处理"""
        # Arrange
        with patch('stream_controller.LocalFFmpegController.start_push') as mock_start:
            mock_start.side_effect = Exception("Internal error")
            
            data = {'path': 'test_video.mp4'}
            
            # Act
            response = client.post('/api/start',
                                 data=json.dumps(data),
                                 content_type='application/json')
            
            # Assert
            assert response.status_code == 500
            result = json.loads(response.data)
            assert result['code'] == -1
            assert 'error' in result['msg'].lower() or '错误' in result['msg']
    
    def test_api_method_not_allowed(self, client):
        """测试不允许的HTTP方法"""
        # Arrange & Act
        response = client.get('/api/start')  # start应该只接受POST
        
        # Assert
        assert response.status_code == 405
    
    def test_api_not_found(self, client):
        """测试不存在的API端点"""
        # Arrange & Act
        response = client.get('/api/nonexistent')
        
        # Assert
        assert response.status_code == 404
    
    def test_api_large_payload(self, client):
        """测试大负载请求"""
        # Arrange
        large_data = {'path': 'x' * 10000}  # 很大的路径
        
        # Act
        response = client.post('/api/start',
                             data=json.dumps(large_data),
                             content_type='application/json')
        
        # Assert
        # 应该被拒绝或正确处理
        assert response.status_code in [400, 413, 500]


class TestAPIRateLimit:
    """API速率限制测试"""
    
    def test_api_rate_limit(self, client):
        """测试API速率限制"""
        # 如果实现了速率限制，测试连续请求
        responses = []
        
        for i in range(10):  # 发送10个连续请求
            response = client.get('/api/status')
            responses.append(response.status_code)
        
        # Assert
        # 如果实现了速率限制，某些请求应该被拒绝
        # 如果没有实现，所有请求都应该成功
        success_count = sum(1 for status in responses if status == 200)
        assert success_count >= 1  # 至少有一个请求成功


class TestWebSocketEvents:
    """WebSocket事件测试"""
    
    def test_websocket_connection(self, mock_socketio):
        """测试WebSocket连接"""
        # 这需要实际的WebSocket测试框架
        pytest.skip("需要WebSocket测试框架")
    
    def test_websocket_monitor_data(self, mock_socketio):
        """测试WebSocket监控数据推送"""
        # 测试监控数据是否正确推送到WebSocket
        pytest.skip("需要WebSocket测试框架")
    
    def test_websocket_status_updates(self, mock_socketio):
        """测试WebSocket状态更新"""
        # 测试状态变化是否通过WebSocket推送
        pytest.skip("需要WebSocket测试框架")
