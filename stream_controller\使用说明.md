# FFmpeg推流控制器使用说明

## 问题解决

### ❌ 原始问题
```
No module named stream_controller
```

### ✅ 问题原因
Flask在debug模式下的自动重载机制在Windows环境下会导致模块导入问题。

### 🔧 解决方案

#### 方案1: 使用新的启动脚本（推荐）
```bash
# 生产模式启动（推荐）
python start_server.py

# 调试模式启动
python start_server.py --debug

# 自定义端口
python start_server.py --port 8080

# 帮助信息
python start_server.py --help
```

#### 方案2: 使用批处理文件（Windows用户）
双击 `启动服务器.bat` 文件

#### 方案3: 直接运行原脚本（已修复）
```bash
python stream_controller.py
```

## 启动方式对比

| 启动方式 | 优点 | 适用场景 |
|---------|------|----------|
| `start_server.py` | 参数灵活，错误处理完善 | 开发和生产环境 |
| `启动服务器.bat` | 一键启动，用户友好 | Windows普通用户 |
| `stream_controller.py` | 简单直接 | 快速测试 |

## 功能特性

### 🎬 核心功能
- **本地视频推流**: 将本地视频文件推送到RTMP服务器
- **无缝切换**: 实时切换视频文件，观众端无感知
- **Web界面控制**: 现代化的Web管理界面
- **队列播放**: 支持播放列表和自动播放
- **🆕 实时监控**: FFmpeg进程性能监控和日志分析
- **🆕 WebSocket通信**: 实时数据推送，无需刷新页面
- **🆕 可视化图表**: CPU、内存、比特率实时图表展示
- **🆕 WebRTC播放**: 低延迟WebRTC视频播放，支持多协议切换
- **🆕 多协议支持**: HLS、FLV、WebRTC三种播放协议
- **🆕 播放统计**: 实时播放质量监控和错误统计

### 📺 支持格式
- 视频格式: MP4, AVI, MKV, MOV
- 推流协议: RTMP, HLS
- 播放协议: HLS, WebRTC

### 🌐 访问地址
- Web管理界面: http://127.0.0.1:5000
- **FFmpeg监控面板: http://127.0.0.1:5000/monitor** ⭐
- HLS播放地址: http://127.0.0.1:80/live/stream1/hls.m3u8
- RTMP推流地址: rtmp://127.0.0.1:1935/live/stream1

## 环境要求

### 必需软件
- Python 3.7+
- FFmpeg（需添加到系统PATH）
- ZLMediaKit服务器（Docker或本地安装）

### Python依赖
```bash
# 自动安装依赖（推荐）
python install_requirements.py

# 或手动安装
pip install flask psutil flask-socketio
```

## 配置说明

### 默认配置
```python
video_dir = r"D:\Dev\ZLMediaKit\ZLMediaKit\Video-files"  # 视频文件目录
rtmp_url = "rtmp://127.0.0.1:1935/live/stream1"          # RTMP推流地址
```

### 自定义配置
修改 `stream_controller.py` 中的配置项：
```python
def __init__(self):
    self.video_dir = r"你的视频目录路径"
    self.rtmp_url = "你的RTMP服务器地址"
```

## 使用流程

### 1. 启动ZLMediaKit服务器
```bash
# Docker方式
docker run -d --name zlmediakit \
  -p 1935:1935 -p 8080:80 -p 8554:554 \
  zlmediakit/zlmediakit:latest

# 或使用本地编译版本
./MediaServer
```

### 2. 启动推流控制器
```bash
python start_server.py
```

### 3. 访问Web界面
打开浏览器，访问 http://127.0.0.1:5000

### 4. 开始推流
1. 在Web界面选择视频文件
2. 点击"开始推流"按钮
3. 在播放器中查看效果

## 故障排除

### 常见问题

#### 1. 模块导入错误
```
No module named stream_controller
```
**解决**: 使用 `start_server.py` 启动脚本

#### 2. FFmpeg未找到
```
FileNotFoundError: [Errno 2] No such file or directory: 'ffmpeg'
```
**解决**: 安装FFmpeg并添加到系统PATH

#### 3. 端口被占用
```
[Errno 10048] Only one usage of each socket address
```
**解决**: 使用不同端口 `python start_server.py --port 8080`

#### 4. 视频文件未找到
**解决**: 检查视频目录路径是否正确

### 调试模式
启用调试模式获取详细错误信息：
```bash
python start_server.py --debug
```

## 技术架构

### 核心组件
- **LocalFFmpegController**: 推流控制逻辑
- **Flask Web应用**: HTTP API和Web界面
- **FFmpeg进程管理**: 子进程控制和监控
- **文件管理**: 视频文件扫描和管理

### API接口
- `GET /api/files`: 获取视频文件列表
- `POST /api/start`: 开始推流
- `POST /api/stop`: 停止推流
- `POST /api/switch`: 切换视频
- `GET /api/status`: 获取状态信息

## 开发指南

### 代码结构
```
stream_controller/
├── stream_controller.py    # 主程序
├── start_server.py        # 启动脚本
├── 启动服务器.bat         # Windows批处理
├── 使用说明.md           # 本文档
└── advanced_local_manager.html  # 静态页面版本
```

### 扩展功能
- 添加更多视频格式支持
- 实现推流质量监控
- 添加用户认证功能
- 支持多路推流

## 更新日志

### v1.3.0 (当前版本)
- ✅ **新增WebRTC播放功能**
- ✅ **集成多协议播放器（HLS/FLV/WebRTC）**
- ✅ **添加播放质量监控和统计**
- ✅ 支持协议自动降级和错误处理
- ✅ 实时播放器性能数据展示
- ✅ ZLMediaKit WebRTC API集成

### v1.2.0
- ✅ **新增FFmpeg实时监控面板**
- ✅ **集成WebSocket实时通信**
- ✅ **添加性能图表和日志分析**
- ✅ 支持CPU、内存、比特率监控
- ✅ 实时FFmpeg日志解析和分类
- ✅ 自动依赖检查和安装脚本

### v1.1.0
- ✅ 修复Windows下Flask debug模式模块导入问题
- ✅ 添加专用启动脚本 `start_server.py`
- ✅ 添加Windows批处理启动文件
- ✅ 改进错误处理和用户提示
- ✅ 添加命令行参数支持

### v1.0.0
- ✅ 基础推流功能
- ✅ Web管理界面
- ✅ 无缝视频切换
- ✅ 队列播放支持 