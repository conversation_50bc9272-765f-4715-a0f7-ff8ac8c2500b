#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FFmpeg推流控制器依赖安装脚本
"""

import subprocess
import sys
import os

def install_package(package):
    """安装Python包"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        return True
    except subprocess.CalledProcessError:
        return False

def check_package(package):
    """检查包是否已安装"""
    try:
        __import__(package)
        return True
    except ImportError:
        return False

def main():
    print("=" * 60)
    print("🔧 FFmpeg推流控制器依赖检查与安装")
    print("=" * 60)
    
    required_packages = [
        ('flask', 'Flask'),
        ('psutil', 'psutil'),
        ('flask_socketio', 'Flask-SocketIO'),
        ('requests', 'requests'),
    ]
    
    missing_packages = []
    
    # 检查依赖
    print("📋 检查依赖包...")
    for import_name, pip_name in required_packages:
        if check_package(import_name):
            print(f"✅ {pip_name} - 已安装")
        else:
            print(f"❌ {pip_name} - 未安装")
            missing_packages.append(pip_name)
    
    # 安装缺失的包
    if missing_packages:
        print(f"\n📦 需要安装 {len(missing_packages)} 个包...")
        for package in missing_packages:
            print(f"正在安装 {package}...")
            if install_package(package):
                print(f"✅ {package} 安装成功")
            else:
                print(f"❌ {package} 安装失败")
                print("请手动运行: pip install", package)
    else:
        print("\n🎉 所有依赖包都已安装！")
    
    # 检查FFmpeg
    print("\n🎬 检查FFmpeg...")
    try:
        result = subprocess.run(['ffmpeg', '-version'], 
                              capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            print("✅ FFmpeg - 已安装")
            # 提取版本信息
            version_line = result.stdout.split('\n')[0]
            print(f"   版本: {version_line}")
        else:
            print("❌ FFmpeg - 未找到或无法运行")
    except (subprocess.TimeoutExpired, FileNotFoundError):
        print("❌ FFmpeg - 未安装或未添加到PATH")
        print("   请从 https://ffmpeg.org/download.html 下载并安装FFmpeg")
        print("   并确保将FFmpeg添加到系统PATH环境变量")
    
    print("\n" + "=" * 60)
    print("🚀 安装完成！现在可以运行推流控制器了")
    print("   启动命令: python start_server.py")
    print("   监控界面: http://127.0.0.1:5000/monitor")
    print("=" * 60)

if __name__ == '__main__':
    main() 