/**
 * ZLMediaKit WebRTC播放器
 * 基于ZLMediaKit的WebRTC客户端实现
 */

class ZLMWebRTCPlayer {
    constructor(video, config) {
        this.video = video;
        this.config = {
            api_url: 'http://127.0.0.1:8080/index/api/webrtc',
            app: 'live',
            stream: 'stream1',
            vhost: '__defaultVhost__',
            ...config
        };
        
        this.pc = null;
        this.sessionId = null;
        this.isPlaying = false;
        this.stats = {
            bytesReceived: 0,
            packetsReceived: 0,
            packetsLost: 0,
            fps: 0,
            bitrate: 0,
            resolution: ''
        };
    }

    async play() {
        try {
            // 1. 创建RTCPeerConnection
            this.pc = new RTCPeerConnection({
                iceServers: [
                    { urls: 'stun:stun.l.google.com:19302' }
                ]
            });

            // 2. 设置事件监听
            this.setupPeerConnectionEvents();

            // 3. 创建Offer
            const offer = await this.pc.createOffer({
                offerToReceiveAudio: true,
                offerToReceiveVideo: true
            });
            
            await this.pc.setLocalDescription(offer);

            // 4. 发送Offer到ZLMediaKit
            const response = await this.sendOfferToZLM(offer);
            
            if (response.code === 0) {
                // 5. 设置远程描述
                const answer = new RTCSessionDescription({
                    type: 'answer',
                    sdp: response.sdp
                });
                
                await this.pc.setRemoteDescription(answer);
                this.sessionId = response.id;
                this.isPlaying = true;
                
                console.log('WebRTC播放开始，会话ID:', this.sessionId);
                return true;
            } else {
                throw new Error(`ZLMediaKit错误: ${response.msg}`);
            }
        } catch (error) {
            console.error('WebRTC播放失败:', error);
            this.stop();
            throw error;
        }
    }

    async sendOfferToZLM(offer) {
        const data = {
            api: 'play',
            type: 'push',
            sdp: offer.sdp,
            app: this.config.app,
            stream: this.config.stream,
            vhost: this.config.vhost
        };

        const response = await fetch(this.config.api_url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });

        return await response.json();
    }

    setupPeerConnectionEvents() {
        // ICE候选事件
        this.pc.onicecandidate = (event) => {
            if (event.candidate) {
                console.log('ICE候选:', event.candidate);
            }
        };

        // ICE连接状态变化
        this.pc.oniceconnectionstatechange = () => {
            console.log('ICE连接状态:', this.pc.iceConnectionState);
            if (this.pc.iceConnectionState === 'failed' || 
                this.pc.iceConnectionState === 'disconnected') {
                this.onError('ICE连接失败');
            }
        };

        // 连接状态变化
        this.pc.onconnectionstatechange = () => {
            console.log('连接状态:', this.pc.connectionState);
        };

        // 接收远程流
        this.pc.ontrack = (event) => {
            console.log('接收到远程流:', event.streams[0]);
            if (this.video) {
                this.video.srcObject = event.streams[0];
                this.video.play().catch(e => {
                    console.error('视频播放失败:', e);
                });
            }
        };

        // 数据通道
        this.pc.ondatachannel = (event) => {
            console.log('数据通道:', event.channel);
        };
    }

    async stop() {
        this.isPlaying = false;
        
        // 关闭PeerConnection
        if (this.pc) {
            this.pc.close();
            this.pc = null;
        }

        // 清空视频源
        if (this.video) {
            this.video.srcObject = null;
        }

        // 通知ZLMediaKit关闭会话
        if (this.sessionId) {
            try {
                await fetch(this.config.api_url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        api: 'close',
                        id: this.sessionId
                    })
                });
            } catch (error) {
                console.warn('关闭会话失败:', error);
            }
            this.sessionId = null;
        }

        console.log('WebRTC播放已停止');
    }

    async getStats() {
        if (!this.pc || !this.isPlaying) {
            return this.stats;
        }

        try {
            const stats = await this.pc.getStats();
            let bytesReceived = 0;
            let packetsReceived = 0;
            let packetsLost = 0;
            let fps = 0;
            let width = 0;
            let height = 0;

            stats.forEach(report => {
                if (report.type === 'inbound-rtp' && report.mediaType === 'video') {
                    bytesReceived += report.bytesReceived || 0;
                    packetsReceived += report.packetsReceived || 0;
                    packetsLost += report.packetsLost || 0;
                    fps = report.framesPerSecond || 0;
                    width = report.frameWidth || 0;
                    height = report.frameHeight || 0;
                }
            });

            this.stats = {
                bytesReceived,
                packetsReceived,
                packetsLost,
                fps,
                bitrate: this.calculateBitrate(bytesReceived),
                resolution: width && height ? `${width}x${height}` : ''
            };

            return this.stats;
        } catch (error) {
            console.error('获取统计信息失败:', error);
            return this.stats;
        }
    }

    calculateBitrate(bytesReceived) {
        const now = Date.now();
        if (!this.lastStatsTime) {
            this.lastStatsTime = now;
            this.lastBytesReceived = bytesReceived;
            return 0;
        }

        const timeDiff = now - this.lastStatsTime;
        const bytesDiff = bytesReceived - this.lastBytesReceived;
        
        if (timeDiff > 0) {
            const bitrate = (bytesDiff * 8) / (timeDiff / 1000) / 1000; // kbps
            this.lastStatsTime = now;
            this.lastBytesReceived = bytesReceived;
            return bitrate;
        }

        return 0;
    }

    onError(error) {
        console.error('WebRTC播放器错误:', error);
        if (this.onErrorCallback) {
            this.onErrorCallback(error);
        }
    }

    setErrorCallback(callback) {
        this.onErrorCallback = callback;
    }
}

// 导出到全局作用域
window.ZLMWebRTCPlayer = ZLMWebRTCPlayer; 