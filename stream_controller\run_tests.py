#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试运行脚本
支持不同类型的测试运行和报告生成
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path


def run_command(cmd, description=""):
    """运行命令并处理结果"""
    print(f"\n{'='*60}")
    print(f"执行: {description}")
    print(f"命令: {' '.join(cmd)}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print(result.stdout)
        if result.stderr:
            print("警告信息:")
            print(result.stderr)
        return True
    except subprocess.CalledProcessError as e:
        print(f"错误: 命令执行失败 (返回码: {e.returncode})")
        print("标准输出:")
        print(e.stdout)
        print("错误输出:")
        print(e.stderr)
        return False


def install_dependencies():
    """安装测试依赖"""
    print("安装测试依赖...")
    
    # 检查是否存在requirements.txt
    req_file = Path("tests/requirements.txt")
    if not req_file.exists():
        print(f"警告: {req_file} 不存在")
        return False
    
    cmd = [sys.executable, "-m", "pip", "install", "-r", str(req_file)]
    return run_command(cmd, "安装测试依赖")


def run_unit_tests(verbose=False, coverage=True):
    """运行单元测试"""
    cmd = [sys.executable, "-m", "pytest"]
    
    # 添加标记过滤
    cmd.extend(["-m", "unit or not (integration or performance)"])
    
    if verbose:
        cmd.append("-v")
    
    if coverage:
        cmd.extend(["--cov=stream_controller", "--cov-report=html", "--cov-report=term"])
    
    return run_command(cmd, "运行单元测试")


def run_integration_tests(verbose=False):
    """运行集成测试"""
    cmd = [sys.executable, "-m", "pytest", "-m", "integration"]
    
    if verbose:
        cmd.append("-v")
    
    return run_command(cmd, "运行集成测试")


def run_security_tests(verbose=False):
    """运行安全测试"""
    cmd = [sys.executable, "-m", "pytest", "-m", "security"]
    
    if verbose:
        cmd.append("-v")
    
    return run_command(cmd, "运行安全测试")


def run_performance_tests(verbose=False):
    """运行性能测试"""
    cmd = [sys.executable, "-m", "pytest", "-m", "performance"]
    
    if verbose:
        cmd.append("-v")
    
    return run_command(cmd, "运行性能测试")


def run_all_tests(verbose=False, parallel=False):
    """运行所有测试"""
    cmd = [sys.executable, "-m", "pytest"]
    
    if verbose:
        cmd.append("-v")
    
    if parallel:
        cmd.extend(["-n", "auto"])
    
    return run_command(cmd, "运行所有测试")


def run_code_quality_checks():
    """运行代码质量检查"""
    checks = [
        {
            "cmd": [sys.executable, "-m", "flake8", "stream_controller.py", "tests/"],
            "desc": "代码风格检查 (flake8)"
        },
        {
            "cmd": [sys.executable, "-m", "black", "--check", "stream_controller.py", "tests/"],
            "desc": "代码格式检查 (black)"
        },
        {
            "cmd": [sys.executable, "-m", "isort", "--check-only", "stream_controller.py", "tests/"],
            "desc": "导入排序检查 (isort)"
        },
        {
            "cmd": [sys.executable, "-m", "mypy", "stream_controller.py"],
            "desc": "类型检查 (mypy)"
        }
    ]
    
    all_passed = True
    for check in checks:
        if not run_command(check["cmd"], check["desc"]):
            all_passed = False
    
    return all_passed


def run_security_scan():
    """运行安全扫描"""
    checks = [
        {
            "cmd": [sys.executable, "-m", "bandit", "-r", "stream_controller.py"],
            "desc": "安全漏洞扫描 (bandit)"
        },
        {
            "cmd": [sys.executable, "-m", "safety", "check"],
            "desc": "依赖安全检查 (safety)"
        }
    ]
    
    all_passed = True
    for check in checks:
        if not run_command(check["cmd"], check["desc"]):
            all_passed = False
    
    return all_passed


def generate_test_report():
    """生成测试报告"""
    cmd = [
        sys.executable, "-m", "pytest",
        "--html=reports/test_report.html",
        "--self-contained-html",
        "--cov=stream_controller",
        "--cov-report=html:reports/coverage",
        "--junit-xml=reports/junit.xml"
    ]
    
    # 创建报告目录
    os.makedirs("reports", exist_ok=True)
    
    return run_command(cmd, "生成测试报告")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Stream Controller 测试运行器")
    parser.add_argument("--install", action="store_true", help="安装测试依赖")
    parser.add_argument("--unit", action="store_true", help="运行单元测试")
    parser.add_argument("--integration", action="store_true", help="运行集成测试")
    parser.add_argument("--security", action="store_true", help="运行安全测试")
    parser.add_argument("--performance", action="store_true", help="运行性能测试")
    parser.add_argument("--all", action="store_true", help="运行所有测试")
    parser.add_argument("--quality", action="store_true", help="运行代码质量检查")
    parser.add_argument("--scan", action="store_true", help="运行安全扫描")
    parser.add_argument("--report", action="store_true", help="生成测试报告")
    parser.add_argument("--verbose", "-v", action="store_true", help="详细输出")
    parser.add_argument("--parallel", "-p", action="store_true", help="并行运行测试")
    parser.add_argument("--full", action="store_true", help="完整测试流程")
    
    args = parser.parse_args()
    
    # 如果没有指定任何选项，显示帮助
    if not any(vars(args).values()):
        parser.print_help()
        return
    
    success = True
    
    # 安装依赖
    if args.install or args.full:
        if not install_dependencies():
            success = False
    
    # 代码质量检查
    if args.quality or args.full:
        if not run_code_quality_checks():
            success = False
    
    # 安全扫描
    if args.scan or args.full:
        if not run_security_scan():
            success = False
    
    # 运行测试
    if args.unit:
        if not run_unit_tests(args.verbose):
            success = False
    
    if args.integration:
        if not run_integration_tests(args.verbose):
            success = False
    
    if args.security:
        if not run_security_tests(args.verbose):
            success = False
    
    if args.performance:
        if not run_performance_tests(args.verbose):
            success = False
    
    if args.all or args.full:
        if not run_all_tests(args.verbose, args.parallel):
            success = False
    
    # 生成报告
    if args.report or args.full:
        if not generate_test_report():
            success = False
    
    # 输出结果
    print(f"\n{'='*60}")
    if success:
        print("✅ 所有检查都通过了！")
        sys.exit(0)
    else:
        print("❌ 某些检查失败了，请查看上面的错误信息。")
        sys.exit(1)


if __name__ == "__main__":
    main()
