#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
队列管理相关的单元测试
采用TDD方式：先写测试，再实现功能
"""

import pytest
import threading
import time
import os
from unittest.mock import patch, Mock, MagicMock
from stream_controller import LocalFFmpegController


class TestQueueBasicOperations:
    """队列基本操作测试"""
    
    def test_add_to_queue_success(self, controller_instance, sample_video_file):
        """测试成功添加到队列"""
        # Arrange
        filepath = sample_video_file
        
        # Act
        result = controller_instance.add_to_queue(filepath)
        
        # Assert
        assert result['code'] == 0
        assert '添加' in result['msg'] or 'add' in result['msg'].lower()
        assert len(controller_instance.play_queue) == 1
        assert controller_instance.play_queue[0] == filepath
    
    def test_add_to_queue_file_not_exist(self, controller_instance):
        """测试添加不存在的文件到队列"""
        # Arrange
        nonexistent_file = "/path/to/nonexistent/file.mp4"
        
        # Act
        result = controller_instance.add_to_queue(nonexistent_file)
        
        # Assert
        assert result['code'] != 0
        assert '不存在' in result['msg'] or 'not exist' in result['msg'].lower()
        assert len(controller_instance.play_queue) == 0
    
    def test_add_to_queue_duplicate_file(self, controller_instance, sample_video_file):
        """测试添加重复文件到队列"""
        # Arrange
        filepath = sample_video_file
        controller_instance.add_to_queue(filepath)  # 先添加一次
        
        # Act
        result = controller_instance.add_to_queue(filepath)  # 再添加一次
        
        # Assert
        # 根据实现，可能允许重复或拒绝重复
        if result['code'] == 0:
            assert len(controller_instance.play_queue) == 2  # 允许重复
        else:
            assert len(controller_instance.play_queue) == 1  # 拒绝重复
            assert '重复' in result['msg'] or 'duplicate' in result['msg'].lower()
    
    def test_remove_from_queue_success(self, controller_instance, sample_video_file):
        """测试成功从队列移除"""
        # Arrange
        filepath = sample_video_file
        controller_instance.add_to_queue(filepath)
        
        # Act
        if hasattr(controller_instance, 'remove_from_queue'):
            result = controller_instance.remove_from_queue(0)  # 移除第一个
            
            # Assert
            assert result['code'] == 0
            assert len(controller_instance.play_queue) == 0
        else:
            pytest.skip("remove_from_queue method not implemented yet")
    
    def test_remove_from_queue_invalid_index(self, controller_instance, sample_video_file):
        """测试移除无效索引"""
        # Arrange
        filepath = sample_video_file
        controller_instance.add_to_queue(filepath)
        
        # Act
        if hasattr(controller_instance, 'remove_from_queue'):
            result = controller_instance.remove_from_queue(10)  # 无效索引
            
            # Assert
            assert result['code'] != 0
            assert '索引' in result['msg'] or 'index' in result['msg'].lower()
            assert len(controller_instance.play_queue) == 1  # 队列不变
        else:
            pytest.skip("remove_from_queue method not implemented yet")
    
    def test_clear_queue_success(self, controller_instance, temp_video_dir):
        """测试清空队列"""
        # Arrange
        # 添加多个文件到队列
        for i in range(3):
            filepath = os.path.join(temp_video_dir, f"video{i}.mp4")
            with open(filepath, 'w') as f:
                f.write("test content")
            controller_instance.add_to_queue(filepath)
        
        # Act
        result = controller_instance.clear_queue()
        
        # Assert
        assert result['code'] == 0
        assert '清空' in result['msg'] or 'clear' in result['msg'].lower()
        assert len(controller_instance.play_queue) == 0
    
    def test_get_queue_status(self, controller_instance, temp_video_dir):
        """测试获取队列状态"""
        # Arrange
        # 添加文件到队列
        filepaths = []
        for i in range(3):
            filepath = os.path.join(temp_video_dir, f"video{i}.mp4")
            with open(filepath, 'w') as f:
                f.write("test content")
            controller_instance.add_to_queue(filepath)
            filepaths.append(filepath)
        
        # Act
        result = controller_instance.get_queue()
        
        # Assert
        assert result['code'] == 0
        assert 'queue' in result
        assert len(result['queue']) == 3
        assert result['queue'] == filepaths


class TestQueuePlayback:
    """队列播放测试"""
    
    def test_start_queue_playback(self, controller_instance, temp_video_dir, mock_subprocess):
        """测试开始队列播放"""
        # Arrange
        mock_popen, mock_process = mock_subprocess
        
        # 添加文件到队列
        filepaths = []
        for i in range(3):
            filepath = os.path.join(temp_video_dir, f"video{i}.mp4")
            with open(filepath, 'w') as f:
                f.write("test content")
            controller_instance.add_to_queue(filepath)
            filepaths.append(filepath)
        
        # Act
        if hasattr(controller_instance, 'start_queue_playback'):
            result = controller_instance.start_queue_playback()
            
            # Assert
            assert result['code'] == 0
            assert controller_instance.is_playing is True
            assert controller_instance.current_file == filepaths[0]
        else:
            pytest.skip("start_queue_playback method not implemented yet")
    
    def test_queue_auto_advance(self, controller_instance, temp_video_dir, mock_subprocess):
        """测试队列自动推进"""
        # Arrange
        mock_popen, mock_process = mock_subprocess
        
        # 模拟进程结束
        mock_process.poll.side_effect = [None, None, 0]  # 前两次返回None，第三次返回0（进程结束）
        
        # 添加文件到队列
        filepaths = []
        for i in range(3):
            filepath = os.path.join(temp_video_dir, f"video{i}.mp4")
            with open(filepath, 'w') as f:
                f.write("test content")
            controller_instance.add_to_queue(filepath)
            filepaths.append(filepath)
        
        # Act
        if hasattr(controller_instance, 'start_queue_playback'):
            controller_instance.start_queue_playback()
            
            # 等待队列线程处理
            time.sleep(0.1)
            
            # Assert
            # 应该自动切换到下一个文件
            # 具体实现取决于队列管理逻辑
            pass
        else:
            pytest.skip("start_queue_playback method not implemented yet")
    
    def test_stop_queue_playback(self, controller_instance, temp_video_dir, mock_subprocess):
        """测试停止队列播放"""
        # Arrange
        mock_popen, mock_process = mock_subprocess
        
        # 添加文件并开始播放
        filepath = os.path.join(temp_video_dir, "video.mp4")
        with open(filepath, 'w') as f:
            f.write("test content")
        controller_instance.add_to_queue(filepath)
        
        if hasattr(controller_instance, 'start_queue_playback'):
            controller_instance.start_queue_playback()
            
            # Act
            if hasattr(controller_instance, 'stop_queue_playback'):
                result = controller_instance.stop_queue_playback()
                
                # Assert
                assert result['code'] == 0
                assert controller_instance.is_playing is False
                assert controller_instance.queue_thread is None or not controller_instance.queue_thread.is_alive()
            else:
                pytest.skip("stop_queue_playback method not implemented yet")
        else:
            pytest.skip("start_queue_playback method not implemented yet")
    
    def test_queue_loop_mode(self, controller_instance, temp_video_dir, mock_subprocess):
        """测试队列循环模式"""
        # Arrange
        mock_popen, mock_process = mock_subprocess
        
        # 添加文件到队列
        filepath = os.path.join(temp_video_dir, "video.mp4")
        with open(filepath, 'w') as f:
            f.write("test content")
        controller_instance.add_to_queue(filepath)
        
        # Act
        if hasattr(controller_instance, 'set_loop_mode'):
            controller_instance.set_loop_mode(True)
            
            # Assert
            assert controller_instance.loop_mode is True
        else:
            pytest.skip("set_loop_mode method not implemented yet")


class TestQueueThreadSafety:
    """队列线程安全测试"""
    
    def test_concurrent_queue_operations(self, controller_instance, temp_video_dir):
        """测试并发队列操作"""
        # Arrange
        filepaths = []
        for i in range(10):
            filepath = os.path.join(temp_video_dir, f"video{i}.mp4")
            with open(filepath, 'w') as f:
                f.write("test content")
            filepaths.append(filepath)
        
        # Act
        def add_files():
            for filepath in filepaths[:5]:
                controller_instance.add_to_queue(filepath)
        
        def remove_files():
            time.sleep(0.01)  # 稍微延迟
            for i in range(3):
                if hasattr(controller_instance, 'remove_from_queue'):
                    try:
                        controller_instance.remove_from_queue(0)
                    except:
                        pass  # 忽略并发错误
        
        # 启动并发线程
        thread1 = threading.Thread(target=add_files)
        thread2 = threading.Thread(target=remove_files)
        
        thread1.start()
        thread2.start()
        
        thread1.join()
        thread2.join()
        
        # Assert
        # 队列应该保持一致状态，不应该崩溃
        assert isinstance(controller_instance.play_queue, list)
        assert len(controller_instance.play_queue) >= 0
    
    def test_queue_modification_during_playback(self, controller_instance, temp_video_dir, mock_subprocess):
        """测试播放期间修改队列"""
        # Arrange
        mock_popen, mock_process = mock_subprocess
        
        # 添加文件到队列
        filepaths = []
        for i in range(5):
            filepath = os.path.join(temp_video_dir, f"video{i}.mp4")
            with open(filepath, 'w') as f:
                f.write("test content")
            controller_instance.add_to_queue(filepath)
            filepaths.append(filepath)
        
        # 开始播放
        if hasattr(controller_instance, 'start_queue_playback'):
            controller_instance.start_queue_playback()
            
            # Act - 在播放期间修改队列
            new_filepath = os.path.join(temp_video_dir, "new_video.mp4")
            with open(new_filepath, 'w') as f:
                f.write("test content")
            
            result = controller_instance.add_to_queue(new_filepath)
            
            # Assert
            assert result['code'] == 0
            assert new_filepath in controller_instance.play_queue
        else:
            pytest.skip("start_queue_playback method not implemented yet")


class TestQueuePersistence:
    """队列持久化测试"""
    
    def test_save_queue_to_file(self, controller_instance, temp_video_dir):
        """测试保存队列到文件"""
        # Arrange
        # 添加文件到队列
        filepaths = []
        for i in range(3):
            filepath = os.path.join(temp_video_dir, f"video{i}.mp4")
            with open(filepath, 'w') as f:
                f.write("test content")
            controller_instance.add_to_queue(filepath)
            filepaths.append(filepath)
        
        # Act
        if hasattr(controller_instance, 'save_queue'):
            queue_file = os.path.join(temp_video_dir, "queue.json")
            result = controller_instance.save_queue(queue_file)
            
            # Assert
            assert result['code'] == 0
            assert os.path.exists(queue_file)
        else:
            pytest.skip("save_queue method not implemented yet")
    
    def test_load_queue_from_file(self, controller_instance, temp_video_dir):
        """测试从文件加载队列"""
        # Arrange
        # 创建队列文件
        filepaths = []
        for i in range(3):
            filepath = os.path.join(temp_video_dir, f"video{i}.mp4")
            with open(filepath, 'w') as f:
                f.write("test content")
            filepaths.append(filepath)
        
        queue_file = os.path.join(temp_video_dir, "queue.json")
        import json
        with open(queue_file, 'w') as f:
            json.dump({'queue': filepaths}, f)
        
        # Act
        if hasattr(controller_instance, 'load_queue'):
            result = controller_instance.load_queue(queue_file)
            
            # Assert
            assert result['code'] == 0
            assert controller_instance.play_queue == filepaths
        else:
            pytest.skip("load_queue method not implemented yet")


class TestQueueErrorHandling:
    """队列错误处理测试"""
    
    def test_queue_with_corrupted_file(self, controller_instance, temp_video_dir, mock_subprocess):
        """测试队列中包含损坏文件的处理"""
        # Arrange
        mock_popen, mock_process = mock_subprocess
        mock_process.poll.return_value = 1  # 模拟FFmpeg失败
        
        # 创建"损坏"的文件
        corrupted_file = os.path.join(temp_video_dir, "corrupted.mp4")
        with open(corrupted_file, 'w') as f:
            f.write("not a video file")
        
        controller_instance.add_to_queue(corrupted_file)
        
        # Act
        if hasattr(controller_instance, 'start_queue_playback'):
            result = controller_instance.start_queue_playback()
            
            # 等待处理
            time.sleep(0.1)
            
            # Assert
            # 应该跳过损坏的文件或报告错误
            # 具体行为取决于实现
            pass
        else:
            pytest.skip("start_queue_playback method not implemented yet")
    
    def test_queue_empty_playback(self, controller_instance):
        """测试空队列播放"""
        # Arrange
        controller_instance.play_queue = []
        
        # Act
        if hasattr(controller_instance, 'start_queue_playback'):
            result = controller_instance.start_queue_playback()
            
            # Assert
            assert result['code'] != 0
            assert '空' in result['msg'] or 'empty' in result['msg'].lower()
        else:
            pytest.skip("start_queue_playback method not implemented yet")
    
    def test_queue_file_deleted_during_playback(self, controller_instance, temp_video_dir, mock_subprocess):
        """测试播放期间文件被删除"""
        # Arrange
        mock_popen, mock_process = mock_subprocess
        
        filepath = os.path.join(temp_video_dir, "video.mp4")
        with open(filepath, 'w') as f:
            f.write("test content")
        
        controller_instance.add_to_queue(filepath)
        
        if hasattr(controller_instance, 'start_queue_playback'):
            controller_instance.start_queue_playback()
            
            # Act - 删除正在播放的文件
            os.remove(filepath)
            
            # 等待处理
            time.sleep(0.1)
            
            # Assert
            # 应该优雅地处理文件不存在的情况
            # 具体行为取决于实现
            pass
        else:
            pytest.skip("start_queue_playback method not implemented yet")
