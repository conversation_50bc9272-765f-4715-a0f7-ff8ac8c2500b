#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
pytest配置文件和共享fixtures
"""

import pytest
import os
import tempfile
import shutil
from unittest.mock import Mock, patch, MagicMock
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from stream_controller import LocalFFmpegController


@pytest.fixture
def temp_video_dir():
    """创建临时视频目录"""
    temp_dir = tempfile.mkdtemp()
    yield temp_dir
    shutil.rmtree(temp_dir, ignore_errors=True)


@pytest.fixture
def sample_video_file(temp_video_dir):
    """创建示例视频文件"""
    video_file = os.path.join(temp_video_dir, "test_video.mp4")
    # 创建一个空文件作为测试用
    with open(video_file, 'w') as f:
        f.write("fake video content")
    return video_file


@pytest.fixture
def mock_subprocess():
    """模拟subprocess模块"""
    with patch('subprocess.Popen') as mock_popen:
        mock_process = Mock()
        mock_process.pid = 12345
        mock_process.poll.return_value = None
        mock_process.terminate.return_value = None
        mock_process.kill.return_value = None
        mock_process.wait.return_value = 0
        mock_process.stdout = Mock()
        mock_process.stderr = Mock()
        mock_popen.return_value = mock_process
        yield mock_popen, mock_process


@pytest.fixture
def mock_psutil():
    """模拟psutil模块"""
    with patch('psutil.Process') as mock_process_class:
        mock_process = Mock()
        mock_process.cpu_percent.return_value = 25.5
        mock_process.memory_info.return_value = Mock(rss=1024*1024*100)  # 100MB
        mock_process.is_running.return_value = True
        mock_process_class.return_value = mock_process
        yield mock_process


@pytest.fixture
def mock_requests():
    """模拟requests模块"""
    with patch('requests.post') as mock_post:
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {'code': 0, 'msg': 'success'}
        mock_post.return_value = mock_response
        yield mock_post


@pytest.fixture
def controller_instance(temp_video_dir):
    """创建控制器实例"""
    controller = LocalFFmpegController()
    controller.video_dir = temp_video_dir
    return controller


@pytest.fixture
def mock_ffprobe():
    """模拟ffprobe命令"""
    mock_output = {
        "streams": [
            {
                "codec_type": "video",
                "width": 1920,
                "height": 1080,
                "codec_name": "h264",
                "avg_frame_rate": "30/1"
            },
            {
                "codec_type": "audio",
                "codec_name": "aac",
                "sample_rate": "44100",
                "channels": 2
            }
        ],
        "format": {
            "duration": "120.0",
            "size": "10485760"
        }
    }
    
    with patch('subprocess.run') as mock_run:
        mock_result = Mock()
        mock_result.returncode = 0
        mock_result.stdout = json.dumps(mock_output)
        mock_result.stderr = ""
        mock_run.return_value = mock_result
        yield mock_run


@pytest.fixture
def flask_app():
    """创建Flask测试应用"""
    from stream_controller import create_app
    app = create_app()
    app.config['TESTING'] = True
    return app


@pytest.fixture
def client(flask_app):
    """创建Flask测试客户端"""
    return flask_app.test_client()


class MockSocketIO:
    """模拟SocketIO"""
    def __init__(self):
        self.emitted_events = []
    
    def emit(self, event, data, **kwargs):
        self.emitted_events.append({'event': event, 'data': data, 'kwargs': kwargs})


@pytest.fixture
def mock_socketio():
    """模拟SocketIO实例"""
    return MockSocketIO()


# 测试数据常量
TEST_VIDEO_INFO = {
    'width': 1920,
    'height': 1080,
    'codec_name': 'h264',
    'avg_frame_rate': '30/1',
    'preset': 'veryfast',
    'sample_rate': 44100,
    'channels': 2
}

TEST_RTMP_URL = "rtmp://test.example.com/live/stream"
TEST_BACKUP_RTMP_URL = "rtmp://backup.example.com/live/stream"

# 测试用的FFmpeg命令模板
EXPECTED_FFMPEG_CMD = [
    'ffmpeg', '-re', '-stream_loop', '-1',
    '-i', 'test_video.mp4',
    '-copyts', '-vsync', 'cfr', '-muxdelay', '0',
    '-flvflags', 'no_duration_filesize',
    '-c:v', 'libx264', '-preset', 'veryfast', '-tune', 'zerolatency',
    '-c:a', 'aac', '-ar', '44100', '-b:a', '128k',
    '-pix_fmt', 'yuv420p', '-g', '50',
    '-f', 'flv', TEST_RTMP_URL
]
