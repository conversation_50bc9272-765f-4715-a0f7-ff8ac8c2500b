name: Docker

on:
  push:
    branches:
      - "master"
      - "feature/*"
      - "release/*"

env:
  # Use docker.io for Docker Hub if empty
  REGISTRY: docker.io
  IMAGE_NAME: zlmediakit/zlmediakit

jobs:
  build:

    runs-on: ubuntu-24.04
    permissions:
      contents: read
      packages: write
      # This is used to complete the identity challenge
      # with sigstore/fulcio when running outside of PRs.
      id-token: write

    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: 下载submodule源码
        run: mv -f .gitmodules_github .gitmodules && git submodule sync && git submodule update --init

      # Install the cosign tool except on PR
      # https://github.com/sigstore/cosign-installer
      - name: Install cosign
        uses: sigstore/cosign-installer@d572c9c13673d2e0a26fabf90b5748f36886883f

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v2


      # Workaround: https://github.com/docker/build-push-action/issues/461
      - name: Setup Docker buildx
        uses: docker/setup-buildx-action@79abd3f86f79a9d68a23c75a09a9a85889262adf

      # Login against a Docker registry except on PR
      # https://github.com/docker/login-action
      - name: Log into registry ${{ env.REGISTRY }}
        uses: docker/login-action@28218f9b04b4f3f62068d7b6ce6ca5b26e35336c
        with:
          registry: ${{ env.REGISTRY }}
          username: zlmediakit
          password: ${{ secrets.DOCKER_IO_SECRET }}

      # Extract metadata (tags, labels) for Docker
      # https://github.com/docker/metadata-action
      - name: Extract Docker metadata
        id: meta
        uses: docker/metadata-action@98669ae865ea3cffbcbaa878cf57c20bbf1c6c38
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}

      # Build and push Docker image with Buildx (don't push on PR)
      # https://github.com/docker/build-push-action
      - name: Build and push Docker image
        if: github.event_name != 'pull_request' && github.ref != 'refs/heads/feature/test'
        id: build-and-push
        uses: docker/build-push-action@ac9327eae2b366085ac7f6a2d02df8aa8ead720a
        with:
          context: .
          push: ${{ github.event_name != 'pull_request' }}
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          build-args: MODEL=Release
          platforms: linux/amd64,linux/arm64

      # Sign the resulting Docker image digest except on PRs.
      # This will only write to the public Rekor transparency log when the Docker
      # repository is public to avoid leaking data.  If you would like to publish
      # transparency data even for private images, pass --force to cosign below.
      # https://github.com/sigstore/cosign
#      - name: Sign the published Docker image
#        if: ${{ github.event_name != 'pull_request' }}
#        env:
#          COSIGN_EXPERIMENTAL: "true"
#        # This step uses the identity token to provision an ephemeral certificate
#        # against the sigstore community Fulcio instance.
#        run: cosign sign ${{ steps.meta.outputs.tags }}@${{ steps.build-and-push.outputs.digest }}
