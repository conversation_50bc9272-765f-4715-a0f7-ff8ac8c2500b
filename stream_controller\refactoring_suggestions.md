# Stream Controller 重构建议

## 🏗️ 架构重构方案

### 1. 分层架构设计
```
stream_controller/
├── core/                   # 核心业务逻辑
│   ├── __init__.py
│   ├── controller.py       # 主控制器
│   ├── ffmpeg_manager.py   # FFmpeg进程管理
│   ├── monitor.py          # 监控模块
│   └── video_analyzer.py   # 视频分析
├── api/                    # API层
│   ├── __init__.py
│   ├── routes.py          # 路由定义
│   ├── middleware.py      # 中间件
│   └── validators.py      # 输入验证
├── config/                 # 配置管理
│   ├── __init__.py
│   ├── settings.py        # 配置类
│   └── constants.py       # 常量定义
├── utils/                  # 工具函数
│   ├── __init__.py
│   ├── file_utils.py      # 文件操作
│   ├── security.py        # 安全工具
│   └── exceptions.py      # 自定义异常
└── static/                 # 静态资源
    ├── css/
    ├── js/
    └── templates/
```

### 2. 核心类重构

#### 配置管理类
```python
# config/settings.py
import os
from dataclasses import dataclass
from typing import Optional

@dataclass
class StreamConfig:
    """流媒体配置"""
    video_dir: str
    rtmp_url: str
    backup_rtmp_url: str
    zlmedia_api_url: str
    zlmedia_secret: Optional[str] = None
    
    @classmethod
    def from_env(cls):
        return cls(
            video_dir=os.getenv('VIDEO_DIR', r'D:\Dev\ZLMediaKit\ZLMediaKit\Video-files'),
            rtmp_url=os.getenv('RTMP_URL', 'rtmp://127.0.0.1:1935/live/stream1'),
            backup_rtmp_url=os.getenv('BACKUP_RTMP_URL', 'rtmp://127.0.0.1:1935/live/stream2'),
            zlmedia_api_url=os.getenv('ZLMEDIA_API_URL', 'http://127.0.0.1:8080/index/api'),
            zlmedia_secret=os.getenv('ZLMEDIA_SECRET')
        )

@dataclass
class AppConfig:
    """应用配置"""
    debug: bool = False
    host: str = '0.0.0.0'
    port: int = 5000
    secret_key: str = 'default_secret_key'
    api_key: Optional[str] = None
```

#### FFmpeg管理器
```python
# core/ffmpeg_manager.py
import subprocess
import threading
from typing import Optional, Dict, Any
from utils.exceptions import FFmpegError, ProcessError

class FFmpegManager:
    """FFmpeg进程管理器"""
    
    def __init__(self, config: StreamConfig):
        self.config = config
        self.processes: Dict[str, subprocess.Popen] = {}
        self.lock = threading.Lock()
    
    def start_stream(self, stream_id: str, filepath: str, 
                    rtmp_url: str, video_info: Dict[str, Any]) -> Dict[str, Any]:
        """启动推流"""
        with self.lock:
            if stream_id in self.processes:
                raise ProcessError(f"Stream {stream_id} already running")
            
            try:
                cmd = self._build_ffmpeg_command(filepath, rtmp_url, video_info)
                process = subprocess.Popen(
                    cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    creationflags=subprocess.CREATE_NEW_PROCESS_GROUP
                )
                
                self.processes[stream_id] = process
                return {'code': 0, 'msg': '推流启动成功', 'pid': process.pid}
                
            except Exception as e:
                raise FFmpegError(f"启动推流失败: {str(e)}")
    
    def stop_stream(self, stream_id: str) -> Dict[str, Any]:
        """停止推流"""
        with self.lock:
            if stream_id not in self.processes:
                return {'code': 0, 'msg': '推流未运行'}
            
            try:
                process = self.processes[stream_id]
                process.terminate()
                process.wait(timeout=5)
                del self.processes[stream_id]
                return {'code': 0, 'msg': '推流已停止'}
                
            except subprocess.TimeoutExpired:
                process.kill()
                del self.processes[stream_id]
                return {'code': 0, 'msg': '推流已强制停止'}
            except Exception as e:
                raise ProcessError(f"停止推流失败: {str(e)}")
    
    def _build_ffmpeg_command(self, filepath: str, rtmp_url: str, 
                             video_info: Dict[str, Any]) -> list:
        """构建FFmpeg命令"""
        return [
            'ffmpeg', '-re', '-stream_loop', '-1',
            '-i', filepath,
            '-copyts', '-vsync', 'cfr', '-muxdelay', '0',
            '-flvflags', 'no_duration_filesize',
            '-c:v', 'libx264',
            '-preset', video_info.get('preset', 'veryfast'),
            '-tune', 'zerolatency',
            '-c:a', 'aac', '-ar', str(video_info.get('sample_rate', 44100)),
            '-b:a', '128k', '-pix_fmt', 'yuv420p', '-g', '50',
            '-f', 'flv', rtmp_url
        ]
```

#### 视频分析器
```python
# core/video_analyzer.py
import json
import subprocess
from typing import Dict, Any
from utils.exceptions import VideoAnalysisError

class VideoAnalyzer:
    """视频分析器"""
    
    @staticmethod
    def analyze_video(filepath: str) -> Dict[str, Any]:
        """分析视频文件"""
        try:
            probe_cmd = [
                'ffprobe', '-v', 'quiet', '-print_format', 'json',
                '-show_format', '-show_streams', filepath
            ]
            
            result = subprocess.run(
                probe_cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                timeout=10
            )
            
            if result.returncode != 0:
                raise VideoAnalysisError(f"ffprobe failed: {result.stderr}")
            
            probe_data = json.loads(result.stdout)
            return VideoAnalyzer._extract_video_info(probe_data)
            
        except subprocess.TimeoutExpired:
            raise VideoAnalysisError("视频分析超时")
        except json.JSONDecodeError:
            raise VideoAnalysisError("无法解析视频信息")
        except Exception as e:
            raise VideoAnalysisError(f"视频分析失败: {str(e)}")
    
    @staticmethod
    def _extract_video_info(probe_data: Dict[str, Any]) -> Dict[str, Any]:
        """提取视频信息"""
        video_stream = None
        audio_stream = None
        
        for stream in probe_data.get('streams', []):
            if stream.get('codec_type') == 'video' and not video_stream:
                video_stream = stream
            elif stream.get('codec_type') == 'audio' and not audio_stream:
                audio_stream = stream
        
        video_info = {
            'width': 1920,
            'height': 1080,
            'preset': 'veryfast',
            'avg_frame_rate': '30/1',
            'sample_rate': 44100,
            'channels': 2
        }
        
        if video_stream:
            video_info.update({
                'width': int(video_stream.get('width', 1920)),
                'height': int(video_stream.get('height', 1080)),
                'codec_name': video_stream.get('codec_name', 'h264'),
                'avg_frame_rate': video_stream.get('avg_frame_rate', '30/1')
            })
            
            # 根据分辨率调整预设
            if video_info['width'] > 1920 or video_info['height'] > 1080:
                video_info['preset'] = 'faster'
        
        if audio_stream:
            video_info.update({
                'audio_codec': audio_stream.get('codec_name', 'aac'),
                'sample_rate': int(audio_stream.get('sample_rate', 44100)),
                'channels': int(audio_stream.get('channels', 2))
            })
        
        return video_info
```

### 3. API层重构

#### 输入验证器
```python
# api/validators.py
from flask import request, jsonify
from functools import wraps
import os
from utils.security import validate_file_path, validate_api_key

def validate_json_input(required_fields=None):
    """验证JSON输入"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not request.is_json:
                return jsonify({'code': -1, 'msg': '请求必须是JSON格式'}), 400
            
            data = request.get_json()
            if required_fields:
                missing_fields = [field for field in required_fields if field not in data]
                if missing_fields:
                    return jsonify({
                        'code': -1, 
                        'msg': f'缺少必需字段: {", ".join(missing_fields)}'
                    }), 400
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def validate_file_path_input(f):
    """验证文件路径输入"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        data = request.get_json()
        filepath = data.get('path')
        
        if not filepath:
            return jsonify({'code': -1, 'msg': '文件路径不能为空'}), 400
        
        if not validate_file_path(filepath):
            return jsonify({'code': -1, 'msg': '非法文件路径'}), 400
        
        return f(*args, **kwargs)
    return decorated_function

def require_api_key(f):
    """要求API密钥验证"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not validate_api_key(request):
            return jsonify({'code': -1, 'msg': '未授权访问'}), 401
        return f(*args, **kwargs)
    return decorated_function
```

### 4. 安全工具
```python
# utils/security.py
import os
import hashlib
import secrets
from flask import request
from config.settings import AppConfig

def validate_file_path(filepath: str, base_dir: str = None) -> bool:
    """验证文件路径安全性"""
    if not filepath:
        return False
    
    # 移除危险字符
    if '..' in filepath or filepath.startswith('/') or ':' in filepath:
        return False
    
    if base_dir:
        try:
            abs_path = os.path.abspath(os.path.join(base_dir, filepath))
            abs_base = os.path.abspath(base_dir)
            return abs_path.startswith(abs_base)
        except (OSError, ValueError):
            return False
    
    return True

def validate_api_key(request) -> bool:
    """验证API密钥"""
    api_key = request.headers.get('X-API-Key')
    expected_key = AppConfig().api_key
    
    if not expected_key:
        return True  # 如果未设置API密钥，则不验证
    
    return api_key == expected_key

def generate_secure_token() -> str:
    """生成安全令牌"""
    return secrets.token_urlsafe(32)

def hash_password(password: str) -> str:
    """密码哈希"""
    salt = secrets.token_hex(16)
    pwdhash = hashlib.pbkdf2_hmac('sha256', password.encode('utf-8'), salt.encode('utf-8'), 100000)
    return salt + pwdhash.hex()

def verify_password(stored_password: str, provided_password: str) -> bool:
    """验证密码"""
    salt = stored_password[:32]
    stored_hash = stored_password[32:]
    pwdhash = hashlib.pbkdf2_hmac('sha256', provided_password.encode('utf-8'), salt.encode('utf-8'), 100000)
    return pwdhash.hex() == stored_hash
```

## 🎯 重构实施计划

### 阶段1: 基础重构 (1-2周)
1. 创建新的目录结构
2. 拆分配置管理
3. 实现基础安全验证
4. 重构核心控制器类

### 阶段2: 功能模块化 (2-3周)
1. 独立FFmpeg管理器
2. 独立视频分析器
3. 独立监控模块
4. 重构API路由

### 阶段3: 安全加固 (1周)
1. 完善输入验证
2. 实现身份认证
3. 添加访问控制
4. 安全测试

### 阶段4: 优化完善 (1周)
1. 性能优化
2. 错误处理完善
3. 文档更新
4. 测试覆盖

通过这个重构方案，可以显著提升代码的可维护性、安全性和扩展性。
