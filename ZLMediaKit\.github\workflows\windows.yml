name: Windows

on: [push, pull_request]

jobs:
  build:
    runs-on: windows-2022

    steps:
      - uses: actions/checkout@v1

      - name: 下载submodule源码
        run: mv -Force .gitmodules_github .gitmodules && git submodule sync && git submodule update --init

      - name: 配置 vcpkg
        uses: lukka/run-vcpkg@v7
        with:
          vcpkgDirectory: '${{github.workspace}}/vcpkg'
          vcpkgTriplet: x64-windows-static
          # 2024.06.01
          vcpkgGitCommitId: '47364fbc300756f64f7876b549d9422d5f3ec0d3'
          vcpkgArguments: 'openssl libsrtp[openssl] usrsctp'

      - name: 编译
        uses: lukka/run-cmake@v3
        with:
          useVcpkgToolchainFile: true
          buildDirectory: '${{github.workspace}}/build'
          cmakeAppendedArgs: ''
          cmakeBuildType: 'Release'

      - name: 设置环境变量
        run: |
          $dateString = Get-Date -Format "yyyy-MM-dd"
          $branch = $env:GITHUB_REF -replace "refs/heads/", "" -replace "[\\/\\\?\%\*:\|\x22<>]", "_"
          $branch2 = $env:GITHUB_REF -replace "refs/heads/", ""
          echo "BRANCH=$branch" >> $env:GITHUB_ENV
          echo "BRANCH2=$branch2" >> $env:GITHUB_ENV
          echo "DATE=$dateString" >> $env:GITHUB_ENV

      - name: 打包二进制
        id: upload
        uses: actions/upload-artifact@v4
        with:
          name: ${{ github.workflow }}_${{ env.BRANCH }}_${{ env.DATE }}
          path: release/*
          if-no-files-found: error
          retention-days: 90

      - name: issue评论
        if: github.event_name != 'pull_request' && github.ref != 'refs/heads/feature/test'
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            github.rest.issues.createComment({
              issue_number: ${{vars.VERSION_ISSUE_NO}},
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: '- 下载地址: [${{ github.workflow }}_${{ env.BRANCH }}_${{ env.DATE }}](${{ steps.upload.outputs.artifact-url }})\n'
                + '- 分支: ${{ env.BRANCH2 }}\n' 
                + '- git hash: ${{ github.sha }} \n' 
                + '- 编译日期: ${{ env.DATE }}\n' 
                + '- 编译记录: [${{ github.run_id }}](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }})\n'
                + '- 打包ci名: ${{ github.workflow }}\n' 
                + '- 开启特性: openssl/webrtc/datachannel\n'
                + '- 说明: 此二进制为x64版本\n' 
            })
