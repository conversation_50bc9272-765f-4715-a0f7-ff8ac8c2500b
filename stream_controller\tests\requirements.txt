# 测试依赖包
pytest>=7.0.0
pytest-cov>=4.0.0
pytest-mock>=3.10.0
pytest-asyncio>=0.21.0
pytest-xdist>=3.0.0
pytest-html>=3.1.0
pytest-benchmark>=4.0.0

# 代码质量检查
flake8>=6.0.0
black>=23.0.0
isort>=5.12.0
mypy>=1.0.0

# 安全检查
bandit>=1.7.0
safety>=2.3.0

# 覆盖率报告
coverage>=7.0.0

# Mock和测试工具
responses>=0.23.0
freezegun>=1.2.0
factory-boy>=3.2.0

# 性能测试
locust>=2.14.0

# 文档生成
sphinx>=6.0.0
sphinx-rtd-theme>=1.2.0
