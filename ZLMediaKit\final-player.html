<!DOCTYPE html>
<html>
<head>
    <title>视频播放测试 - HTTP-FLV</title>
    <script src="https://cdn.jsdelivr.net/npm/flv.js/dist/flv.min.js"></script>
</head>
<body>
    <h1>MP4视频流播放测试</h1>
    
    <h2>HTTP-FLV播放 (稳定)</h2>
    <video id="videoPlayer" width="640" height="360" controls autoplay muted></video>
    
    <div style="margin-top: 20px;">
        <p><strong>流地址:</strong> http://localhost:8080/live/stream1.flv</p>
        <p><strong>状态:</strong> <span id="status">准备就绪</span></p>
    </div>

    <script>
        const video = document.getElementById('videoPlayer');
        const status = document.getElementById('status');
        
        if (flvjs.isSupported()) {
            const flvPlayer = flvjs.createPlayer({
                type: 'flv',
                url: 'http://localhost:8080/live/stream1.flv'
            });
            
            flvPlayer.attachMediaElement(video);
            flvPlayer.load();
            flvPlayer.play();
            
            status.textContent = '正在播放HTTP-FLV流';
            
            flvPlayer.on(flvjs.Events.ERROR, (errorType, errorDetail, errorInfo) => {
                status.textContent = `播放错误: ${errorType} - ${errorDetail}`;
                console.error('FLV播放错误:', errorType, errorDetail, errorInfo);
            });
        } else {
            status.textContent = '浏览器不支持FLV播放';
        }
    </script>

    <h2>WebRTC播放 (需要特定浏览器)</h2>
    <p>直接访问: <a href="http://localhost:8080/webrtc/?schema=rtmp&app=live&stream=stream1&type=play" target="_blank">WebRTC播放器</a></p>
    
    <h2>HLS播放 (iOS/Safari推荐)</h2>
    <video width="640" height="360" controls autoplay muted>
        <source src="http://localhost:8080/live/stream1/hls.m3u8" type="application/x-mpegURL">
        您的浏览器不支持HLS播放
    </video>
</body>
</html>