# Stream Controller 安全性改进建议

## 🔒 安全问题分析

### 1. 硬编码敏感信息
```python
# 问题：API密钥硬编码
self.zlmedia_secret = "035c73f7-bb6b-4889-a715-d9eb2d1925cc"

# 建议：使用环境变量
import os
self.zlmedia_secret = os.getenv('ZLMEDIA_SECRET', 'default_secret')
```

### 2. 路径遍历风险
```python
# 问题：未充分验证文件路径
filepath = self.fix_file_path(filepath)

# 建议：添加路径安全检查
def validate_file_path(self, filepath):
    # 确保路径在允许的目录内
    abs_path = os.path.abspath(filepath)
    allowed_dir = os.path.abspath(self.video_dir)
    return abs_path.startswith(allowed_dir)
```

### 3. 命令注入风险
```python
# 问题：直接使用用户输入构建命令
cmd = ['ffmpeg', '-i', filepath, ...]

# 建议：参数验证和转义
import shlex
def sanitize_path(self, path):
    return shlex.quote(path)
```

### 4. 缺少身份验证
- Web接口无认证机制
- 建议添加JWT或Session认证
- 实现API密钥验证

## 🛡️ 安全加固方案

### 1. 配置文件化
```python
# config.py
import os
from dataclasses import dataclass

@dataclass
class Config:
    VIDEO_DIR: str = os.getenv('VIDEO_DIR', r'D:\Dev\ZLMediaKit\ZLMediaKit\Video-files')
    RTMP_URL: str = os.getenv('RTMP_URL', 'rtmp://127.0.0.1:1935/live/stream1')
    ZLMEDIA_SECRET: str = os.getenv('ZLMEDIA_SECRET')
    API_KEY: str = os.getenv('API_KEY')
    MAX_FILE_SIZE: int = int(os.getenv('MAX_FILE_SIZE', '1073741824'))  # 1GB
```

### 2. 输入验证中间件
```python
from functools import wraps
from flask import request, jsonify

def validate_api_key(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        api_key = request.headers.get('X-API-Key')
        if not api_key or api_key != Config.API_KEY:
            return jsonify({'code': -1, 'msg': '未授权访问'}), 401
        return f(*args, **kwargs)
    return decorated_function
```

### 3. 文件路径安全检查
```python
def secure_path_join(self, base_dir, filename):
    """安全的路径拼接，防止路径遍历"""
    # 移除危险字符
    filename = filename.replace('..', '').replace('/', '').replace('\\', '')
    full_path = os.path.join(base_dir, filename)
    
    # 确保结果路径在基础目录内
    if not os.path.abspath(full_path).startswith(os.path.abspath(base_dir)):
        raise ValueError("非法路径访问")
    
    return full_path
```

### 4. 进程管理安全
```python
def secure_process_management(self):
    """安全的进程管理"""
    # 限制进程数量
    if len(psutil.pids()) > 1000:
        raise RuntimeError("系统进程过多，拒绝启动新进程")
    
    # 设置进程资源限制
    import resource
    resource.setrlimit(resource.RLIMIT_CPU, (300, 300))  # 限制CPU时间
    resource.setrlimit(resource.RLIMIT_AS, (1024*1024*1024, 1024*1024*1024))  # 限制内存
```

## 🔧 实施建议

### 1. 立即修复
- 移除硬编码密钥
- 添加文件路径验证
- 实现基础认证

### 2. 中期改进
- 完善日志记录
- 添加访问控制
- 实现配置管理

### 3. 长期规划
- 集成专业认证系统
- 添加审计日志
- 实现细粒度权限控制
