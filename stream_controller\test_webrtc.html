<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebRTC播放器测试</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; }
        .video-player { width: 100%; height: 400px; background: #000; border-radius: 8px; }
        .controls { margin: 20px 0; text-align: center; }
        .btn { padding: 10px 20px; margin: 5px; border: none; border-radius: 5px; cursor: pointer; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        .protocol-selector { margin: 20px 0; text-align: center; }
        .protocol-btn { padding: 8px 16px; margin: 5px; border: 2px solid #007bff; background: white; color: #007bff; border-radius: 20px; cursor: pointer; }
        .protocol-btn.active { background: #007bff; color: white; }
        .stats { margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 5px; font-family: monospace; }
        .status { text-align: center; margin: 10px 0; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎬 WebRTC播放器测试</h1>
        
        <div class="status" id="status">就绪</div>
        
        <div class="protocol-selector">
            <button class="protocol-btn active" data-protocol="hls">HLS</button>
            <button class="protocol-btn" data-protocol="flv">FLV</button>
            <button class="protocol-btn" data-protocol="webrtc">WebRTC</button>
        </div>
        
        <video id="videoPlayer" class="video-player" controls muted>
            您的浏览器不支持视频播放
        </video>
        
        <div class="controls">
            <button class="btn btn-success" onclick="startPlay()">▶️ 开始播放</button>
            <button class="btn btn-danger" onclick="stopPlay()">⏸️ 停止播放</button>
            <button class="btn btn-primary" onclick="refreshPlayer()">🔄 刷新</button>
        </div>
        
        <div class="stats" id="playerStats">
            等待播放器数据...
        </div>
    </div>

    <!-- JavaScript libraries -->
    <script src="https://cdn.jsdelivr.net/npm/hls.js@latest"></script>
    <script src="https://unpkg.com/flv.js/dist/flv.min.js"></script>
    <script src="webrtc_player.js"></script>

    <script>
        let currentPlayer = null;
        let currentProtocol = 'hls';
        
        // 协议选择
        document.querySelectorAll('.protocol-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                document.querySelectorAll('.protocol-btn').forEach(b => b.classList.remove('active'));
                this.classList.add('active');
                currentProtocol = this.dataset.protocol;
                updateStatus(`已选择${currentProtocol.toUpperCase()}协议`);
            });
        });
        
        function updateStatus(message) {
            document.getElementById('status').textContent = message;
        }
        
        function updateStats(stats) {
            document.getElementById('playerStats').innerHTML = `
                协议: ${stats.protocol || currentProtocol.toUpperCase()}<br>
                URL: ${stats.url || '未知'}<br>
                分辨率: ${stats.resolution || '未知'}<br>
                帧率: ${stats.fps ? stats.fps.toFixed(1) : '0'} FPS<br>
                比特率: ${stats.bitrate ? stats.bitrate.toFixed(0) : '0'} kbps<br>
                错误: ${stats.errors || 0}
            `;
        }
        
        async function startPlay() {
            const video = document.getElementById('videoPlayer');
            const streamId = 'stream1';
            
            try {
                await stopPlay();
                updateStatus(`正在启动${currentProtocol.toUpperCase()}播放...`);
                
                switch(currentProtocol) {
                    case 'hls':
                        await startHLSPlay(video, streamId);
                        break;
                    case 'flv':
                        await startFLVPlay(video, streamId);
                        break;
                    case 'webrtc':
                        await startWebRTCPlay(video, streamId);
                        break;
                }
                
                updateStatus('播放已启动');
            } catch (error) {
                updateStatus(`播放失败: ${error.message}`);
                console.error('播放失败:', error);
            }
        }
        
        async function startHLSPlay(video, streamId) {
            const hlsUrl = `http://127.0.0.1:8080/live/${streamId}/hls.m3u8`;
            
            if (Hls.isSupported()) {
                currentPlayer = new Hls({ debug: false });
                currentPlayer.loadSource(hlsUrl);
                currentPlayer.attachMedia(video);
                currentPlayer.on(Hls.Events.MANIFEST_PARSED, () => video.play());
                
                updateStats({ protocol: 'HLS', url: hlsUrl });
            } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
                video.src = hlsUrl;
                video.play();
                updateStats({ protocol: 'HLS (Native)', url: hlsUrl });
            } else {
                throw new Error('浏览器不支持HLS播放');
            }
        }
        
        async function startFLVPlay(video, streamId) {
            const flvUrl = `http://127.0.0.1:8080/live/${streamId}.live.flv`;
            
            if (flvjs.isSupported()) {
                currentPlayer = flvjs.createPlayer({
                    type: 'flv',
                    url: flvUrl,
                    isLive: true
                });
                
                currentPlayer.attachMediaElement(video);
                currentPlayer.load();
                currentPlayer.on(flvjs.Events.LOADING_COMPLETE, () => video.play());
                
                updateStats({ protocol: 'FLV', url: flvUrl });
            } else {
                throw new Error('浏览器不支持FLV播放');
            }
        }
        
        async function startWebRTCPlay(video, streamId) {
            if (!window.ZLMWebRTCPlayer) {
                throw new Error('WebRTC播放器未加载');
            }
            
            if (!window.RTCPeerConnection) {
                throw new Error('浏览器不支持WebRTC');
            }
            
            currentPlayer = new ZLMWebRTCPlayer(video, {
                stream: streamId,
                app: 'live',
                vhost: '__defaultVhost__'
            });
            
            currentPlayer.setErrorCallback((error) => {
                updateStatus(`WebRTC错误: ${error}`);
            });
            
            await currentPlayer.play();
            updateStats({ 
                protocol: 'WebRTC', 
                url: `webrtc://127.0.0.1:8080/live/${streamId}` 
            });
        }
        
        async function stopPlay() {
            const video = document.getElementById('videoPlayer');
            
            if (currentPlayer) {
                try {
                    if (currentPlayer.stop) {
                        await currentPlayer.stop();
                    } else if (currentPlayer.destroy) {
                        currentPlayer.destroy();
                    } else if (currentPlayer.unload) {
                        currentPlayer.unload();
                    }
                } catch (e) {
                    console.warn('播放器清理失败:', e);
                }
                currentPlayer = null;
            }
            
            video.src = '';
            video.srcObject = null;
            video.load();
            
            updateStatus('播放已停止');
            updateStats({});
        }
        
        function refreshPlayer() {
            startPlay();
        }
        
        // 初始化
        updateStatus('WebRTC播放器测试页面已就绪');
        updateStats({});
        
        console.log('WebRTC播放器类是否可用:', !!window.ZLMWebRTCPlayer);
        console.log('HLS.js是否可用:', !!window.Hls);
        console.log('FLV.js是否可用:', !!window.flvjs);
        console.log('WebRTC是否支持:', !!window.RTCPeerConnection);
    </script>
</body>
</html> 