{"info": {"_postman_id": "8b3cdc62-3e18-4700-9ddd-dc9f58ebce83", "name": "ZLMediaKit", "description": "媒体服务器", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "26338564"}, "item": [{"name": "获取服务器api列表(getApiList)", "request": {"method": "GET", "header": [], "url": {"raw": "{{ZLMediaKit_URL}}/index/api/getApiList?secret={{ZLMediaKit_secret}}", "host": ["{{ZLMediaKit_URL}}"], "path": ["index", "api", "getApiList"], "query": [{"key": "secret", "value": "{{ZLMediaKit_secret}}", "description": "api操作密钥(配置文件配置)"}]}}, "response": []}, {"name": "关闭多屏拼接(stack/stop)", "request": {"method": "GET", "header": [], "url": {"raw": "{{ZLMediaKit_URL}}/index/api/getApiList?secret={{ZLMediaKit_secret}}&id=stack_test", "host": ["{{ZLMediaKit_URL}}"], "path": ["index", "api", "getApiList"], "query": [{"key": "secret", "value": "{{ZLMediaKit_secret}}", "description": "api操作密钥(配置文件配置)"}, {"key": "id", "value": "stack_test"}]}}, "response": []}, {"name": "添加多屏拼接(stack/start)", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"gapv\": 0.002,\r\n    \"gaph\": 0.001,\r\n    \"width\": 1920,\r\n    \"url\": [\r\n        [\r\n            \"rtsp://kkem.me/live/test3\",\r\n            \"rtsp://kkem.me/live/cy1\",\r\n            \"rtsp://kkem.me/live/cy1\",\r\n            \"rtsp://kkem.me/live/cy2\"\r\n        ],\r\n        [\r\n            \"rtsp://kkem.me/live/cy1\",\r\n            \"rtsp://kkem.me/live/cy5\",\r\n            \"rtsp://kkem.me/live/cy3\",\r\n            \"rtsp://kkem.me/live/cy4\"\r\n        ],\r\n        [\r\n            \"rtsp://kkem.me/live/cy5\",\r\n            \"rtsp://kkem.me/live/cy6\",\r\n            \"rtsp://kkem.me/live/cy7\",\r\n            \"rtsp://kkem.me/live/cy8\"\r\n        ],\r\n        [\r\n            \"rtsp://kkem.me/live/cy9\",\r\n            \"rtsp://kkem.me/live/cy10\",\r\n            \"rtsp://kkem.me/live/cy11\",\r\n            \"rtsp://kkem.me/live/cy12\"\r\n        ]\r\n    ],\r\n    \"id\": \"89\",\r\n    \"row\": 4,\r\n    \"col\": 4,\r\n    \"height\": 1080,\r\n    \"span\": [\r\n        [\r\n            [\r\n                0,\r\n                0\r\n            ],\r\n            [\r\n                1,\r\n                1\r\n            ]\r\n        ],\r\n        [\r\n            [\r\n                3,\r\n                0\r\n            ],\r\n            [\r\n                3,\r\n                1\r\n            ]\r\n        ],\r\n        [\r\n            [\r\n                2,\r\n                3\r\n            ],\r\n            [\r\n                3,\r\n                3\r\n            ]\r\n        ]\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{ZLMediaKit_URL}}/index/api/stack/start?secret={{ZLMediaKit_secret}}", "host": ["{{ZLMediaKit_URL}}"], "path": ["index", "api", "stack", "start"], "query": [{"key": "secret", "value": "{{ZLMediaKit_secret}}", "description": "api操作密钥(配置文件配置)"}]}}, "response": []}, {"name": "获取网络线程负载(getThreadsLoad)", "request": {"method": "GET", "header": [], "url": {"raw": "{{ZLMediaKit_URL}}/index/api/getThreadsLoad?secret={{ZLMediaKit_secret}}", "host": ["{{ZLMediaKit_URL}}"], "path": ["index", "api", "getThreadsLoad"], "query": [{"key": "secret", "value": "{{ZLMediaKit_secret}}", "description": "api操作密钥(配置文件配置)"}]}}, "response": []}, {"name": "获取主要对象个数(getStatistic)", "request": {"method": "GET", "header": [], "url": {"raw": "{{ZLMediaKit_URL}}/index/api/getStatistic?secret={{ZLMediaKit_secret}}", "host": ["{{ZLMediaKit_URL}}"], "path": ["index", "api", "getStatistic"], "query": [{"key": "secret", "value": "{{ZLMediaKit_secret}}", "description": "api操作密钥(配置文件配置)"}]}}, "response": []}, {"name": "获取后台线程负载(getWorkThreadsLoad)", "request": {"method": "GET", "header": [], "url": {"raw": "{{ZLMediaKit_URL}}/index/api/getWorkThreadsLoad?secret={{ZLMediaKit_secret}}", "host": ["{{ZLMediaKit_URL}}"], "path": ["index", "api", "getWorkThreadsLoad"], "query": [{"key": "secret", "value": "{{ZLMediaKit_secret}}", "description": "api操作密钥(配置文件配置)"}]}}, "response": []}, {"name": "获取服务器配置(getServerConfig)", "request": {"method": "GET", "header": [], "url": {"raw": "{{ZLMediaKit_URL}}/index/api/getServerConfig?secret={{ZLMediaKit_secret}}", "host": ["{{ZLMediaKit_URL}}"], "path": ["index", "api", "getServerConfig"], "query": [{"key": "secret", "value": "{{ZLMediaKit_secret}}", "description": "api操作密钥(配置文件配置)"}]}}, "response": []}, {"name": "设置服务器配置(setServerConfig)", "request": {"method": "GET", "header": [], "url": {"raw": "{{ZLMediaKit_URL}}/index/api/setServerConfig?secret={{ZLMediaKit_secret}}&api.apiDebug=0", "host": ["{{ZLMediaKit_URL}}"], "path": ["index", "api", "setServerConfig"], "query": [{"key": "secret", "value": "{{ZLMediaKit_secret}}", "description": "api操作密钥(配置文件配置)"}, {"key": "api.apiDebug", "value": "0", "description": "配置键与配置项值"}]}}, "response": []}, {"name": "重启服务器(restartServer)", "request": {"method": "GET", "header": [], "url": {"raw": "{{ZLMediaKit_URL}}/index/api/restartServer?secret={{ZLMediaKit_secret}}", "host": ["{{ZLMediaKit_URL}}"], "path": ["index", "api", "restartServer"], "query": [{"key": "secret", "value": "{{ZLMediaKit_secret}}", "description": "api操作密钥(配置文件配置)"}]}}, "response": []}, {"name": "获取流列表(getMediaList)", "request": {"method": "GET", "header": [], "url": {"raw": "{{ZLMediaKit_URL}}/index/api/getMediaList?secret={{ZLMediaKit_secret}}", "host": ["{{ZLMediaKit_URL}}"], "path": ["index", "api", "getMediaList"], "query": [{"key": "secret", "value": "{{ZLMediaKit_secret}}", "description": "api操作密钥(配置文件配置)"}, {"key": "schema", "value": "rtmp", "description": "筛选协议，例如 rtsp或rtmp", "disabled": true}, {"key": "vhost", "value": "{{defaultVhost}}", "description": "筛选虚拟主机，例如__defaultVhost__", "disabled": true}, {"key": "app", "value": null, "description": "筛选应用名，例如 live", "disabled": true}, {"key": "stream", "value": null, "description": "筛选流id，例如 test", "disabled": true}]}}, "response": []}, {"name": "关断单个流(close_stream)", "request": {"method": "GET", "header": [], "url": {"raw": "{{ZLMediaKit_URL}}/index/api/close_stream?secret={{ZLMediaKit_secret}}&schema=rtsp&vhost={{defaultVhost}}&app=live&stream=test", "host": ["{{ZLMediaKit_URL}}"], "path": ["index", "api", "close_stream"], "query": [{"key": "secret", "value": "{{ZLMediaKit_secret}}", "description": "api操作密钥(配置文件配置)"}, {"key": "schema", "value": "rtsp", "description": "协议，例如 rtsp或rtmp"}, {"key": "vhost", "value": "{{defaultVhost}}", "description": "虚拟主机，例如__defaultVhost__"}, {"key": "app", "value": "live", "description": "应用名，例如 live"}, {"key": "stream", "value": "test", "description": "流id，例如 test"}, {"key": "force", "value": "1", "description": "是否强制关闭(有人在观看是否还关闭)", "disabled": true}]}}, "response": []}, {"name": "批量关断流(close_streams)", "request": {"method": "GET", "header": [], "url": {"raw": "{{ZLMediaKit_URL}}/index/api/close_streams?secret={{ZLMediaKit_secret}}&schema=rtsp&vhost={{defaultVhost}}&app=live&stream=test", "host": ["{{ZLMediaKit_URL}}"], "path": ["index", "api", "close_streams"], "query": [{"key": "secret", "value": "{{ZLMediaKit_secret}}", "description": "api操作密钥(配置文件配置)"}, {"key": "schema", "value": "rtsp", "description": "协议，例如 rtsp或rtmp"}, {"key": "vhost", "value": "{{defaultVhost}}", "description": "虚拟主机，例如__defaultVhost__"}, {"key": "app", "value": "live", "description": "应用名，例如 live"}, {"key": "stream", "value": "test", "description": "流id，例如 test"}, {"key": "force", "value": "1", "description": "是否强制关闭(有人在观看是否还关闭)", "disabled": true}]}}, "response": []}, {"name": "获取Session列表(getAllSession)", "request": {"method": "GET", "header": [], "url": {"raw": "{{ZLMediaKit_URL}}/index/api/getAllSession?secret={{ZLMediaKit_secret}}", "host": ["{{ZLMediaKit_URL}}"], "path": ["index", "api", "getAllSession"], "query": [{"key": "secret", "value": "{{ZLMediaKit_secret}}", "description": "api操作密钥(配置文件配置)"}, {"key": "local_port", "value": "", "description": "筛选本机端口，例如筛选rtsp链接：554", "disabled": true}, {"key": "peer_ip", "value": null, "description": "筛选客户端ip", "disabled": true}]}}, "response": []}, {"name": "断开tcp连接(kick_session)", "request": {"method": "GET", "header": [], "url": {"raw": "{{ZLMediaKit_URL}}/index/api/kick_session?secret={{ZLMediaKit_secret}}&id=123456", "host": ["{{ZLMediaKit_URL}}"], "path": ["index", "api", "kick_session"], "query": [{"key": "secret", "value": "{{ZLMediaKit_secret}}", "description": "api操作密钥(配置文件配置)"}, {"key": "id", "value": "123456", "description": "客户端唯一id，可以通过getAllSession接口获取"}]}}, "response": []}, {"name": "批量断开tcp连接(kick_sessions)", "request": {"method": "GET", "header": [], "url": {"raw": "{{ZLMediaKit_URL}}/index/api/kick_sessions?secret={{ZLMediaKit_secret}}", "host": ["{{ZLMediaKit_URL}}"], "path": ["index", "api", "kick_sessions"], "query": [{"key": "secret", "value": "{{ZLMediaKit_secret}}", "description": "api操作密钥(配置文件配置)"}, {"key": "local_port", "value": "", "description": "筛选本机端口，例如筛选rtsp链接：554", "disabled": true}, {"key": "peer_ip", "value": null, "description": "筛选客户端ip", "disabled": true}]}}, "response": []}, {"name": "添加rtsp/rtmp/hls/srt拉流代理(addStreamProxy)", "request": {"method": "GET", "header": [], "url": {"raw": "{{ZLMediaKit_URL}}/index/api/addStreamProxy?secret={{ZLMediaKit_secret}}&vhost={{defaultVhost}}&app=live&stream=test&url=rtmp://live.hkstv.hk.lxdns.com/live/hks2", "host": ["{{ZLMediaKit_URL}}"], "path": ["index", "api", "addStreamProxy"], "query": [{"key": "secret", "value": "{{ZLMediaKit_secret}}", "description": "api操作密钥(配置文件配置)"}, {"key": "vhost", "value": "{{defaultVhost}}", "description": "添加的流的虚拟主机，例如__defaultVhost__"}, {"key": "app", "value": "live", "description": "添加的流的应用名，例如live"}, {"key": "stream", "value": "test", "description": "添加的流的id名，例如test"}, {"key": "url", "value": "rtmp://live.hkstv.hk.lxdns.com/live/hks2", "description": "拉流地址，例如rtmp://live.hkstv.hk.lxdns.com/live/hks2"}, {"key": "rtp_type", "value": null, "description": "rtsp拉流时，拉流方式，0：tcp，1：udp，2：组播", "disabled": true}, {"key": "timeout_sec", "value": "10", "description": "拉流超时时间，单位秒，float类型", "disabled": true}, {"key": "retry_count", "value": "", "description": "拉流重试次数,不传此参数或传值<=0时，则无限重试", "disabled": true}, {"key": "enable_hls", "value": null, "description": "是否转hls-ts", "disabled": true}, {"key": "enable_hls_fmp4", "value": null, "description": "是否转hls-fmp4", "disabled": true}, {"key": "enable_mp4", "value": null, "description": "是否mp4录制", "disabled": true}, {"key": "enable_rtsp", "value": "1", "description": "是否转协议为rtsp/webrtc", "disabled": true}, {"key": "enable_rtmp", "value": "1", "description": "是否转协议为rtmp/flv", "disabled": true}, {"key": "enable_ts", "value": "1", "description": "是否转协议为http-ts/ws-ts", "disabled": true}, {"key": "enable_fmp4", "value": "1", "description": "是否转协议为http-fmp4/ws-fmp4", "disabled": true}, {"key": "enable_audio", "value": "1", "description": "转协议是否开启音频", "disabled": true}, {"key": "add_mute_audio", "value": "1", "description": "转协议无音频时，是否添加静音aac音频", "disabled": true}, {"key": "mp4_save_path", "value": null, "description": "mp4录制保存根目录，置空使用默认目录", "disabled": true}, {"key": "mp4_max_second", "value": "1800", "description": "mp4录制切片大小，单位秒", "disabled": true}, {"key": "hls_save_path", "value": null, "description": "hls保存根目录，置空使用默认目录", "disabled": true}, {"key": "modify_stamp", "value": null, "description": "是否修改原始时间戳，默认值2；取值范围：0.采用源视频流绝对时间戳，不做任何改变;1.采用zlmediakit接收数据时的系统时间戳(有平滑处理);2.采用源视频流时间戳相对时间戳(增长量)，有做时间戳跳跃和回退矫正", "disabled": true}, {"key": "auto_close", "value": null, "description": "无人观看时，是否直接关闭(而不是通过on_none_reader hook返回close)", "disabled": true}, {"key": "latency", "value": null, "description": "srt延时, 单位毫秒", "disabled": true}, {"key": "passphrase", "value": null, "description": "srt拉流的密码", "disabled": true}]}}, "response": []}, {"name": "关闭拉流代理(delStreamProxy)", "request": {"method": "GET", "header": [], "url": {"raw": "{{ZLMediaKit_URL}}/index/api/delStreamProxy?secret={{ZLMediaKit_secret}}&key=__defaultVhost__/live/1", "host": ["{{ZLMediaKit_URL}}"], "path": ["index", "api", "delStreamProxy"], "query": [{"key": "secret", "value": "{{ZLMediaKit_secret}}", "description": "api操作密钥(配置文件配置)"}, {"key": "key", "value": "__defaultVhost__/live/1", "description": "addStreamProxy接口返回的key"}]}}, "response": []}, {"name": "获取拉流代理列表(listStreamProxy)", "request": {"method": "GET", "header": [], "url": {"raw": "{{ZLMediaKit_URL}}/index/api/listStreamProxy?secret={{ZLMediaKit_secret}}", "host": ["{{ZLMediaKit_URL}}"], "path": ["index", "api", "listStreamProxy"], "query": [{"key": "secret", "value": "{{ZLMediaKit_secret}}", "description": "api操作密钥(配置文件配置)"}]}}, "response": []}, {"name": "获取推流代理列表(listStreamPusherProxy)", "request": {"method": "GET", "header": [], "url": {"raw": "{{ZLMediaKit_URL}}/index/api/listStreamPusherProxy?secret={{ZLMediaKit_secret}}", "host": ["{{ZLMediaKit_URL}}"], "path": ["index", "api", "listStreamPusherProxy"], "query": [{"key": "secret", "value": "{{ZLMediaKit_secret}}", "description": "api操作密钥(配置文件配置)"}]}}, "response": []}, {"name": "添加rtsp/rtmp/srt推流(addStreamPusherProxy)", "request": {"method": "GET", "header": [], "url": {"raw": "{{ZLMediaKit_URL}}/index/api/addStreamPusherProxy?secret={{ZLMediaKit_secret}}&schema=rtmp&vhost={{defaultVhost}}&app=live&stream=test&dst_url=rtmp://*************/live/push", "host": ["{{ZLMediaKit_URL}}"], "path": ["index", "api", "addStreamPusherProxy"], "query": [{"key": "secret", "value": "{{ZLMediaKit_secret}}", "description": "api操作密钥(配置文件配置)"}, {"key": "schema", "value": "rtmp", "description": "推流协议，支持rtsp、rtmp，大小写敏感"}, {"key": "vhost", "value": "{{defaultVhost}}", "description": "已注册流的虚拟主机，一般为__defaultVhost__"}, {"key": "app", "value": "live", "description": "已注册流的应用名，例如live"}, {"key": "stream", "value": "test", "description": "已注册流的id名，例如test"}, {"key": "dst_url", "value": "rtmp://*************/live/push", "description": "推流地址，需要与schema字段协议一致"}, {"key": "rtp_type", "value": "0", "description": "rtsp推流时，推流方式，0：tcp，1：udp", "disabled": true}, {"key": "timeout_sec", "value": "10", "description": "推流超时时间，单位秒，float类型", "disabled": true}, {"key": "retry_count", "value": null, "description": "推流重试次数,不传此参数或传值<=0时，则无限重试", "disabled": true}, {"key": "latency", "value": null, "description": "srt延时, 单位毫秒", "disabled": true}, {"key": "passphrase", "value": null, "description": "srt推流的密码", "disabled": true}]}}, "response": []}, {"name": "关闭推流(delStreamPusherProxy)", "request": {"method": "GET", "header": [], "url": {"raw": "{{ZLMediaKit_URL}}/index/api/delStreamPusherProxy?secret={{ZLMediaKit_secret}}&key=rtmp/__defaultVhost__/live/test/f40a8ab006cac16ecc0858409e890491", "host": ["{{ZLMediaKit_URL}}"], "path": ["index", "api", "delStreamPusherProxy"], "query": [{"key": "secret", "value": "{{ZLMediaKit_secret}}", "description": "api操作密钥(配置文件配置)"}, {"key": "key", "value": "rtmp/__defaultVhost__/live/test/f40a8ab006cac16ecc0858409e890491", "description": "addStreamPusherProxy接口返回的key"}]}}, "response": []}, {"name": "获取FFmpeg拉流代理列表(listFFmpegSource)", "request": {"method": "GET", "header": [], "url": {"raw": "{{ZLMediaKit_URL}}/index/api/listFFmpegSource?secret={{ZLMediaKit_secret}}", "host": ["{{ZLMediaKit_URL}}"], "path": ["index", "api", "listFFmpegSource"], "query": [{"key": "secret", "value": "{{ZLMediaKit_secret}}", "description": "api操作密钥(配置文件配置)"}]}}, "response": []}, {"name": "添加FFmpeg拉流代理(addFFmpegSource)", "request": {"method": "GET", "header": [], "url": {"raw": "{{ZLMediaKit_URL}}/index/api/addFFmpegSource?secret={{ZLMediaKit_secret}}&src_url=http://hefeng.live.tempsource.cjyun.org/videotmp/s10100-hftv.m3u8&dst_url=rtmp://127.0.0.1/live/hks2&timeout_ms=10000&enable_hls=0&enable_mp4=0", "host": ["{{ZLMediaKit_URL}}"], "path": ["index", "api", "addFFmpegSource"], "query": [{"key": "secret", "value": "{{ZLMediaKit_secret}}", "description": "api操作密钥(配置文件配置)"}, {"key": "src_url", "value": "http://hefeng.live.tempsource.cjyun.org/videotmp/s10100-hftv.m3u8", "description": "FFmpeg拉流地址,支持任意协议或格式(只要FFmpeg支持即可)"}, {"key": "dst_url", "value": "rtmp://127.0.0.1/live/hks2", "description": "FFmpeg rtmp推流地址，一般都是推给自己，例如rtmp://127.0.0.1/live/stream_form_ffmpeg"}, {"key": "timeout_ms", "value": "10000", "description": "FFmpeg推流成功超时时间,单位毫秒"}, {"key": "enable_hls", "value": "0", "description": "是否开启hls录制"}, {"key": "enable_mp4", "value": "0", "description": "是否开启mp4录制"}, {"key": "ffmpeg_cmd_key", "value": "ffmpeg.cmd_hd", "description": "FFmpeg命名参数模板，置空则采用配置项:ffmpeg.cmd", "disabled": true}]}}, "response": []}, {"name": "关闭FFmpeg拉流代理(delFFmpegSource)", "request": {"method": "GET", "header": [], "url": {"raw": "{{ZLMediaKit_URL}}/index/api/delFFmpegSource?secret={{ZLMediaKit_secret}}&key=5f748d2ef9712e4b2f6f970c1d44d93a", "host": ["{{ZLMediaKit_URL}}"], "path": ["index", "api", "delFFmpegSource"], "query": [{"key": "secret", "value": "{{ZLMediaKit_secret}}", "description": "api操作密钥(配置文件配置)"}, {"key": "key", "value": "5f748d2ef9712e4b2f6f970c1d44d93a"}]}}, "response": []}, {"name": "流是否在线(isMediaOnline)", "request": {"method": "GET", "header": [], "url": {"raw": "{{ZLMediaKit_URL}}/index/api/isMediaOnline?secret={{ZLMediaKit_secret}}&schema=rtsp&vhost={{defaultVhost}}&app=proxy&stream=1", "host": ["{{ZLMediaKit_URL}}"], "path": ["index", "api", "isMediaOnline"], "query": [{"key": "secret", "value": "{{ZLMediaKit_secret}}", "description": "api操作密钥(配置文件配置)"}, {"key": "schema", "value": "rtsp", "description": "协议，例如 rtsp或rtmp"}, {"key": "vhost", "value": "{{defaultVhost}}", "description": "虚拟主机，例如__defaultVhost__"}, {"key": "app", "value": "proxy", "description": "应用名，例如 live"}, {"key": "stream", "value": "1", "description": "流id，例如 test"}]}}, "response": []}, {"name": "获取媒体流播放器列表(getMediaPlayerList)", "request": {"method": "GET", "header": [], "url": {"raw": "{{ZLMediaKit_URL}}/index/api/getMediaPlayerList?secret={{ZLMediaKit_secret}}&schema=rtsp&vhost={{defaultVhost}}&app=proxy&stream=1", "host": ["{{ZLMediaKit_URL}}"], "path": ["index", "api", "getMediaPlayerList"], "query": [{"key": "secret", "value": "{{ZLMediaKit_secret}}", "description": "api操作密钥(配置文件配置)"}, {"key": "schema", "value": "rtsp", "description": "协议，例如 rtsp或rtmp"}, {"key": "vhost", "value": "{{defaultVhost}}", "description": "虚拟主机，例如__defaultVhost__"}, {"key": "app", "value": "proxy", "description": "应用名，例如 live"}, {"key": "stream", "value": "1", "description": "流id，例如 test"}]}}, "response": []}, {"name": "广播webrtc datachannel消息(broadcastMessage)", "request": {"method": "GET", "header": [], "url": {"raw": "{{ZLMediaKit_URL}}/index/api/broadcastMessage?secret={{ZLMediaKit_secret}}&schema=rtsp&vhost={{defaultVhost}}&app=live&stream=test&msg=Hello ZLMediakit", "host": ["{{ZLMediaKit_URL}}"], "path": ["index", "api", "broadcastMessage"], "query": [{"key": "secret", "value": "{{ZLMediaKit_secret}}", "description": "api操作密钥(配置文件配置)"}, {"key": "schema", "value": "rtsp", "description": "协议，例如 rtsp或rtmp，目前仅支持rtsp协议"}, {"key": "vhost", "value": "{{defaultVhost}}", "description": "虚拟主机，例如__defaultVhost__"}, {"key": "app", "value": "live", "description": "应用名，例如 live"}, {"key": "stream", "value": "test", "description": "流id，例如 test"}, {"key": "msg", "value": "Hello ZLMediakit"}]}}, "response": []}, {"name": "获取流信息(getMediaInfo)", "request": {"method": "GET", "header": [], "url": {"raw": "{{ZLMediaKit_URL}}/index/api/getMediaInfo?secret={{ZLMediaKit_secret}}&schema=rtsp&vhost={{defaultVhost}}&app=live&stream=mym9", "host": ["{{ZLMediaKit_URL}}"], "path": ["index", "api", "getMediaInfo"], "query": [{"key": "secret", "value": "{{ZLMediaKit_secret}}", "description": "api操作密钥(配置文件配置)"}, {"key": "schema", "value": "rtsp", "description": "协议，例如 rtsp或rtmp"}, {"key": "vhost", "value": "{{defaultVhost}}", "description": "虚拟主机，例如__defaultVhost__"}, {"key": "app", "value": "live", "description": "应用名，例如 live"}, {"key": "stream", "value": "mym9", "description": "流id，例如 test"}]}}, "response": []}, {"name": "获取流信息(getMp4RecordFile)", "request": {"method": "GET", "header": [], "url": {"raw": "{{ZLMediaKit_URL}}/index/api/getMp4RecordFile?secret={{ZLMediaKit_secret}}&vhost={{defaultVhost}}&app=proxy&stream=2&customized_path=/www&period=2020-05-26", "host": ["{{ZLMediaKit_URL}}"], "path": ["index", "api", "getMp4RecordFile"], "query": [{"key": "secret", "value": "{{ZLMediaKit_secret}}", "description": "api操作密钥(配置文件配置)"}, {"key": "vhost", "value": "{{defaultVhost}}", "description": "虚拟主机，例如__defaultVhost__"}, {"key": "app", "value": "proxy", "description": "应用名，例如 live"}, {"key": "stream", "value": "2", "description": "流id，例如 test"}, {"key": "customized_path", "value": "/www", "description": "录像文件保存自定义根目录，为空则采用配置文件设置"}, {"key": "period", "value": "2020-05-26", "description": "流的录像日期，格式为2020-02-01,如果不是完整的日期，那么是搜索录像文件夹列表，否则搜索对应日期下的mp4文件列表"}]}}, "response": []}, {"name": "删除录像文件夹(deleteRecordDirectory)", "request": {"method": "GET", "header": [], "url": {"raw": "{{ZLMediaKit_URL}}/index/api/deleteRecordDirectory?secret={{ZLMediaKit_secret}}&vhost={{defaultVhost}}&app=proxy&stream=2&period=2020-01-01", "host": ["{{ZLMediaKit_URL}}"], "path": ["index", "api", "deleteRecordDirectory"], "query": [{"key": "secret", "value": "{{ZLMediaKit_secret}}", "description": "api操作密钥(配置文件配置)"}, {"key": "vhost", "value": "{{defaultVhost}}", "description": "虚拟主机，例如__defaultVhost__"}, {"key": "app", "value": "proxy", "description": "应用名，例如 live"}, {"key": "stream", "value": "2", "description": "流id，例如 test"}, {"key": "period", "value": "2020-01-01", "description": "流的录像日期，格式为2020-01-01,如果不是完整的日期，那么会删除失败"}]}}, "response": []}, {"name": "开始录制(startRecord)", "request": {"method": "GET", "header": [], "url": {"raw": "{{ZLMediaKit_URL}}/index/api/startRecord?secret={{ZLMediaKit_secret}}&type=1&vhost={{defaultVhost}}&app=live&stream=obs", "host": ["{{ZLMediaKit_URL}}"], "path": ["index", "api", "startRecord"], "query": [{"key": "secret", "value": "{{ZLMediaKit_secret}}", "description": "api操作密钥(配置文件配置)"}, {"key": "type", "value": "1", "description": "0为hls，1为mp4"}, {"key": "vhost", "value": "{{defaultVhost}}", "description": "虚拟主机，例如__defaultVhost__"}, {"key": "app", "value": "live", "description": "应用名，例如 live"}, {"key": "stream", "value": "obs", "description": "流id，例如 obs"}, {"key": "customized_path", "value": null, "description": "录像文件保存自定义根目录，为空则采用配置文件设置", "disabled": true}, {"key": "max_second", "value": "1000", "description": "MP4录制的切片时间大小，单位秒", "disabled": true}]}}, "response": []}, {"name": "设置录像速度(setRecordSpeed)", "request": {"method": "GET", "header": [], "url": {"raw": "{{ZLMediaKit_URL}}/index/api/setRecordSpeed?secret={{ZLMediaKit_secret}}&vhost={{defaultVhost}}&app=live&stream=obs&speed=2.0", "host": ["{{ZLMediaKit_URL}}"], "path": ["index", "api", "setRecordSpeed"], "query": [{"key": "secret", "value": "{{ZLMediaKit_secret}}", "description": "api操作密钥(配置文件配置)"}, {"key": "vhost", "value": "{{defaultVhost}}", "description": "虚拟主机，例如__defaultVhost__"}, {"key": "app", "value": "live", "description": "应用名，例如 live"}, {"key": "stream", "value": "obs", "description": "流id，例如 obs"}, {"key": "speed", "value": "2.0", "description": "要设置的录像倍速"}]}}, "response": []}, {"name": "设置录像流播放位置(seekRecordStamp)", "request": {"method": "GET", "header": [], "url": {"raw": "{{ZLMediaKit_URL}}/index/api/seekRecordStamp?secret={{ZLMediaKit_secret}}&vhost={{defaultVhost}}&app=live&stream=obs&stamp=1000", "host": ["{{ZLMediaKit_URL}}"], "path": ["index", "api", "seekRecordStamp"], "query": [{"key": "secret", "value": "{{ZLMediaKit_secret}}", "description": "api操作密钥(配置文件配置)"}, {"key": "vhost", "value": "{{defaultVhost}}", "description": "虚拟主机，例如__defaultVhost__"}, {"key": "app", "value": "live", "description": "应用名，例如 live"}, {"key": "stream", "value": "obs", "description": "流id，例如 obs"}, {"key": "stamp", "value": "1000", "description": "要设置的录像播放位置"}]}}, "response": []}, {"name": "停止录制(stopRecord)", "request": {"method": "GET", "header": [], "url": {"raw": "{{ZLMediaKit_URL}}/index/api/stopRecord?secret={{ZLMediaKit_secret}}&type=1&vhost={{defaultVhost}}&app=live&stream=obs", "host": ["{{ZLMediaKit_URL}}"], "path": ["index", "api", "stopRecord"], "query": [{"key": "secret", "value": "{{ZLMediaKit_secret}}", "description": "api操作密钥(配置文件配置)"}, {"key": "type", "value": "1", "description": "0为hls，1为mp4"}, {"key": "vhost", "value": "{{defaultVhost}}", "description": "虚拟主机，例如__defaultVhost__"}, {"key": "app", "value": "live", "description": "应用名，例如 live"}, {"key": "stream", "value": "obs", "description": "流id，例如 obs"}]}}, "response": []}, {"name": "是否正在录制(isRecording)", "request": {"method": "GET", "header": [], "url": {"raw": "{{ZLMediaKit_URL}}/index/api/isRecording?secret={{ZLMediaKit_secret}}&type=1&vhost={{defaultVhost}}&app=live&stream=obs", "host": ["{{ZLMediaKit_URL}}"], "path": ["index", "api", "isRecording"], "query": [{"key": "secret", "value": "{{ZLMediaKit_secret}}", "description": "api操作密钥(配置文件配置)"}, {"key": "type", "value": "1", "description": "0为hls，1为mp4"}, {"key": "vhost", "value": "{{defaultVhost}}", "description": "虚拟主机，例如__defaultVhost__"}, {"key": "app", "value": "live", "description": "应用名，例如 live"}, {"key": "stream", "value": "obs", "description": "流id，例如 obs"}]}}, "response": []}, {"name": "获取截图(getSnap)", "request": {"method": "GET", "header": [], "url": {"raw": "{{ZLMediaKit_URL}}/index/api/getSnap?secret={{ZLMediaKit_secret}}&url=rtsp://www.mym9.com/101065?from=2019-06-28/01:12:13&timeout_sec=10&expire_sec=1", "host": ["{{ZLMediaKit_URL}}"], "path": ["index", "api", "getSnap"], "query": [{"key": "secret", "value": "{{ZLMediaKit_secret}}", "description": "api操作密钥(配置文件配置)"}, {"key": "url", "value": "rtsp://www.mym9.com/101065?from=2019-06-28/01:12:13", "description": "需要截图的url，可以是本机的，也可以是远程主机的"}, {"key": "timeout_sec", "value": "10", "description": "截图失败超时时间，防止FFmpeg一直等待截图"}, {"key": "expire_sec", "value": "1", "description": "截图的过期时间，该时间内产生的截图都会作为缓存返回"}, {"key": "async", "value": "0", "disabled": true, "description": "是否采用zlm内置播放器、解码器api异步截图，开启后截图速度提升但兼容性降低"}]}}, "response": []}, {"name": "获取rtp推流信息(getRtpInfo)", "request": {"method": "GET", "header": [], "url": {"raw": "{{ZLMediaKit_URL}}/index/api/getRtpInfo?secret={{ZLMediaKit_secret}}&stream_id=test", "host": ["{{ZLMediaKit_URL}}"], "path": ["index", "api", "getRtpInfo"], "query": [{"key": "secret", "value": "{{ZLMediaKit_secret}}", "description": "api操作密钥(配置文件配置)"}, {"key": "vhost", "value": "{{defaultVhost}}", "description": "虚拟主机，例如__defaultVhost__"}, {"key": "app", "value": "rtp", "description": "应用名，例如 rtp"}, {"key": "stream_id", "value": "test", "description": "流id"}]}}, "response": []}, {"name": "创建RTP服务器(openRtpServer)", "request": {"method": "GET", "header": [], "url": {"raw": "{{ZLMediaKit_URL}}/index/api/openRtpServer?secret={{ZLMediaKit_secret}}&port=0&tcp_mode=1&stream_id=test", "host": ["{{ZLMediaKit_URL}}"], "path": ["index", "api", "openRtpServer"], "query": [{"key": "secret", "value": "{{ZLMediaKit_secret}}", "description": "api操作密钥(配置文件配置)"}, {"key": "port", "value": "0", "description": "绑定的端口，0时为随机端口"}, {"key": "tcp_mode", "value": "1", "description": "tcp模式，0时为不启用tcp监听，1时为启用tcp监听，2时为tcp主动连接模式"}, {"key": "vhost", "value": "{{defaultVhost}}", "description": "虚拟主机，例如__defaultVhost__"}, {"key": "app", "value": "rtp", "description": "应用名，例如 rtp"}, {"key": "stream_id", "value": "test", "description": "该端口绑定的流id\n"}, {"key": "re_use_port", "value": "0", "description": "是否重用端口，默认为0，非必选参数", "disabled": true}, {"key": "ssrc", "value": "0", "description": "是否指定收流的rtp ssrc, 十进制数字，不指定或指定0时则不过滤rtp，非必选参数", "disabled": true}, {"key": "only_track", "value": "1", "description": "是否为单音频/单视频track，0：不设置，1：单音频，2：单视频", "disabled": true}, {"key": "local_ip", "value": "::", "description": "指定创建RTP的本地ip，ipv4可填”0.0.0.0“，ipv6可填”::“，一般保持默认", "disabled": true}]}}, "response": []}, {"name": "创建多路复用RTP服务器(openRtpServerMultiplex)", "request": {"method": "GET", "header": [], "url": {"raw": "{{ZLMediaKit_URL}}/index/api/openRtpServerMultiplex?secret={{ZLMediaKit_secret}}&port=0&tcp_mode=1&stream_id=test", "host": ["{{ZLMediaKit_URL}}"], "path": ["index", "api", "openRtpServerMultiplex"], "query": [{"key": "secret", "value": "{{ZLMediaKit_secret}}", "description": "api操作密钥(配置文件配置)"}, {"key": "port", "value": "0", "description": "绑定的端口，0时为随机端口"}, {"key": "tcp_mode", "value": "1", "description": "tcp模式，0时为不启用tcp监听，1时为启用tcp监听"}, {"key": "vhost", "value": "{{defaultVhost}}", "description": "虚拟主机，例如__defaultVhost__"}, {"key": "app", "value": "rtp", "description": "应用名，例如 rtp"}, {"key": "stream_id", "value": "test", "description": "该端口绑定的流id\n"}, {"key": "only_track", "value": "0", "description": "是否为单音频/单视频track，0：不设置，1：单音频，2：单视频", "disabled": true}, {"key": "local_ip", "value": "::", "description": "指定创建RTP的本地ip，ipv4可填”0.0.0.0“，ipv6可填”::“，一般保持默认", "disabled": true}]}}, "response": []}, {"name": "连接RTP服务器(connectRtpServer)", "request": {"method": "GET", "header": [], "url": {"raw": "{{ZLMediaKit_URL}}/index/api/connectRtpServer?secret={{ZLMediaKit_secret}}&dst_url=0&dst_port=1&stream_id=test", "host": ["{{ZLMediaKit_URL}}"], "path": ["index", "api", "connectRtpServer"], "query": [{"key": "secret", "value": "{{ZLMediaKit_secret}}", "description": "api操作密钥(配置文件配置)"}, {"key": "dst_url", "value": "0", "description": "tcp主动模式时服务端地址"}, {"key": "dst_port", "value": "1", "description": "tcp主动模式时服务端端口"}, {"key": "vhost", "value": "{{defaultVhost}}", "description": "虚拟主机，例如__defaultVhost__"}, {"key": "app", "value": "rtp", "description": "应用名，例如 rtp"}, {"key": "stream_id", "value": "test", "description": "OpenRtpServer时绑定的流id\n"}]}}, "response": []}, {"name": "关闭RTP服务器(closeRtpServer)", "request": {"method": "GET", "header": [], "url": {"raw": "{{ZLMediaKit_URL}}/index/api/closeRtpServer?secret={{ZLMediaKit_secret}}&stream_id=test", "host": ["{{ZLMediaKit_URL}}"], "path": ["index", "api", "closeRtpServer"], "query": [{"key": "secret", "value": "{{ZLMediaKit_secret}}", "description": "api操作密钥(配置文件配置)"}, {"key": "vhost", "value": "{{defaultVhost}}", "description": "虚拟主机，例如__defaultVhost__"}, {"key": "app", "value": "rtp", "description": "应用名，例如 rtp"}, {"key": "stream_id", "value": "test", "description": "该端口绑定的流id"}]}}, "response": []}, {"name": "更新RTP服务器过滤SSRC(updateRtpServerSSRC)", "request": {"method": "GET", "header": [], "url": {"raw": "{{ZLMediaKit_URL}}/index/api/updateRtpServerSSRC?secret={{ZLMediaKit_secret}}&stream_id=test&ssrc=123456", "host": ["{{ZLMediaKit_URL}}"], "path": ["index", "api", "updateRtpServerSSRC"], "query": [{"key": "secret", "value": "{{ZLMediaKit_secret}}", "description": "api操作密钥(配置文件配置)"}, {"key": "vhost", "value": "{{defaultVhost}}", "description": "虚拟主机，例如__defaultVhost__"}, {"key": "app", "value": "rtp", "description": "应用名，例如 rtp"}, {"key": "stream_id", "value": "test", "description": "该端口绑定的流id"}, {"key": "ssrc", "value": "123456", "description": "十进制ssrc"}]}}, "response": []}, {"name": "暂停RTP超时检查(pauseRtpCheck)", "request": {"method": "GET", "header": [], "url": {"raw": "{{ZLMediaKit_URL}}/index/api/pauseRtpCheck?secret={{ZLMediaKit_secret}}&stream_id=test", "host": ["{{ZLMediaKit_URL}}"], "path": ["index", "api", "pauseRtpCheck"], "query": [{"key": "secret", "value": "{{ZLMediaKit_secret}}", "description": "api操作密钥(配置文件配置)"}, {"key": "vhost", "value": "{{defaultVhost}}", "description": "虚拟主机，例如__defaultVhost__"}, {"key": "app", "value": "rtp", "description": "应用名，例如 rtp"}, {"key": "stream_id", "value": "test", "description": "该端口绑定的流id"}]}}, "response": []}, {"name": "恢复RTP超时检查(resumeRtpCheck)", "request": {"method": "GET", "header": [], "url": {"raw": "{{ZLMediaKit_URL}}/index/api/resumeRtpCheck?secret={{ZLMediaKit_secret}}&stream_id=test", "host": ["{{ZLMediaKit_URL}}"], "path": ["index", "api", "resumeRtpCheck"], "query": [{"key": "secret", "value": "{{ZLMediaKit_secret}}", "description": "api操作密钥(配置文件配置)"}, {"key": "vhost", "value": "{{defaultVhost}}", "description": "虚拟主机，例如__defaultVhost__"}, {"key": "app", "value": "rtp", "description": "应用名，例如 rtp"}, {"key": "stream_id", "value": "test", "description": "该端口绑定的流id"}]}}, "response": []}, {"name": "获取RTP服务器列表(listRtpServer)", "request": {"method": "GET", "header": [], "url": {"raw": "{{ZLMediaKit_URL}}/index/api/listRtpServer?secret={{ZLMediaKit_secret}}", "host": ["{{ZLMediaKit_URL}}"], "path": ["index", "api", "listRtpServer"], "query": [{"key": "secret", "value": "{{ZLMediaKit_secret}}", "description": "api操作密钥(配置文件配置)"}]}}, "response": []}, {"name": "开始active模式发送rtp(startSendRtp)", "request": {"method": "GET", "header": [], "url": {"raw": "{{ZLMediaKit_URL}}/index/api/startSendRtp?secret={{ZLMediaKit_secret}}&vhost={{defaultVhost}}&app=live&stream=obs&ssrc=1&dst_url=127.0.0.1&dst_port=10000&is_udp=0", "host": ["{{ZLMediaKit_URL}}"], "path": ["index", "api", "startSendRtp"], "query": [{"key": "secret", "value": "{{ZLMediaKit_secret}}", "description": "api操作密钥(配置文件配置)"}, {"key": "vhost", "value": "{{defaultVhost}}", "description": "虚拟主机，例如__defaultVhost__"}, {"key": "app", "value": "live", "description": "应用名，例如 live"}, {"key": "stream", "value": "obs", "description": "流id，例如 obs"}, {"key": "ssrc_multi_send", "value": "0", "description": "是否支持同ssrc推流到多个上级服务器,该参数非必选参数 默认false", "disabled": true}, {"key": "ssrc", "value": "1", "description": "rtp推流的ssrc"}, {"key": "dst_url", "value": "127.0.0.1", "description": "目标ip或域名"}, {"key": "dst_port", "value": "10000", "description": "目标端口"}, {"key": "is_udp", "value": "0", "description": "1:udp active模式, 0:tcp active模式"}, {"key": "src_port", "value": "0", "description": "指定tcp/udp客户端使用的本地端口，0时为随机端口，该参数非必选参数，不传时为随机端口。", "disabled": true}, {"key": "from_mp4", "value": "0", "description": "是否推送本地MP4录像，该参数非必选参数", "disabled": true}, {"key": "type", "value": "1", "description": "rtp打包模式，0:es, 1: ps, 2: ts", "disabled": true}, {"key": "pt", "value": "96", "description": "rtp payload type，默认96，该参数非必选参数", "disabled": true}, {"key": "only_audio", "value": "1", "description": "rtp es方式打包时，是否只打包音频，该参数非必选参数", "disabled": true}, {"key": "udp_rtcp_timeout", "value": "0", "description": "udp方式推流时，是否开启rtcp发送和rtcp接收超时判断，开启后(默认关闭)，如果接收rr rtcp超时，将导致主动停止rtp发送", "disabled": true}, {"key": "recv_stream_id", "value": "", "description": "发送rtp同时接收，一般用于双向语言对讲, 如果不为空，说明开启接收，值为接收流的id", "disabled": true}]}}, "response": []}, {"name": "开始passive模式发送rtp(startSendRtpPassive)", "request": {"method": "GET", "header": [], "url": {"raw": "{{ZLMediaKit_URL}}/index/api/startSendRtpPassive?secret={{ZLMediaKit_secret}}&vhost={{defaultVhost}}&app=live&stream=test&ssrc=1", "host": ["{{ZLMediaKit_URL}}"], "path": ["index", "api", "startSendRtpPassive"], "query": [{"key": "secret", "value": "{{ZLMediaKit_secret}}", "description": "api操作密钥(配置文件配置)"}, {"key": "vhost", "value": "{{defaultVhost}}", "description": "虚拟主机，例如__defaultVhost__"}, {"key": "app", "value": "live", "description": "应用名，例如 live"}, {"key": "stream", "value": "test", "description": "流id，例如 obs"}, {"key": "ssrc", "value": "1", "description": "rtp推流的ssrc，ssrc不同时，可以推流到多个上级服务器"}, {"key": "is_udp", "value": "0", "disabled": true, "description": "1:udp passive模式, 0:tcp passive模式"}, {"key": "src_port", "value": "0", "description": "指定tcp/udp客户端使用的本地端口，0时为随机端口，该参数非必选参数，不传时为随机端口。", "disabled": true}, {"key": "from_mp4", "value": "0", "description": "是否推送本地MP4录像，该参数非必选参数", "disabled": true}, {"key": "type", "value": "1", "description": "rtp打包模式，0:es, 1: ps, 2: ts", "disabled": true}, {"key": "pt", "value": "96", "description": "rtp payload type，默认96，该参数非必选参数", "disabled": true}, {"key": "only_audio", "value": "1", "description": "rtp es方式打包时，是否只打包音频，该参数非必选参数", "disabled": true}, {"key": "recv_stream_id", "value": "", "description": "发送rtp同时接收，一般用于双向语言对讲, 如果不为空，说明开启接收，值为接收流的id", "disabled": true}, {"key": "close_delay_ms", "value": "5000", "description": "等待tcp连接超时时间，单位毫秒，默认5000毫秒", "disabled": true}]}}, "response": []}, {"name": "开始双向对讲(startSendRtpTalk)", "request": {"method": "GET", "header": [], "url": {"raw": "{{ZLMediaKit_URL}}/index/api/startSendRtpTalk?secret={{ZLMediaKit_secret}}&vhost={{defaultVhost}}&app=live&stream=obs&ssrc=1&recv_stream_id=", "host": ["{{ZLMediaKit_URL}}"], "path": ["index", "api", "startSendRtpTalk"], "query": [{"key": "secret", "value": "{{ZLMediaKit_secret}}", "description": "api操作密钥(配置文件配置)"}, {"key": "vhost", "value": "{{defaultVhost}}", "description": "虚拟主机，例如__defaultVhost__"}, {"key": "app", "value": "rtp", "description": "应用名，例如 rtp"}, {"key": "stream", "value": "rtc", "description": "流id，例如webrtc推流上来的流id"}, {"key": "ssrc", "value": "1", "description": "rtp推流出去的ssrc"}, {"key": "recv_stream_id", "value": "", "description": "对方rtp推流上来的流id，我们将通过这个链接回复他rtp流；请注意两个流的app和vhost需一致"}, {"key": "from_mp4", "value": "0", "description": "是否推送本地MP4录像，该参数非必选参数", "disabled": true}, {"key": "type", "value": "1", "description": "0(ES流)、1(PS流)、2(TS流)，默认1(PS流)；该参数非必选参数", "disabled": true}, {"key": "pt", "value": "96", "description": "rtp payload type，默认96；该参数非必选参数", "disabled": true}, {"key": "only_audio", "value": "1", "description": "rtp es方式打包时，是否只打包音频；该参数非必选参数", "disabled": true}]}}, "response": []}, {"name": "停止 发送rtp(stopSendRtp)", "request": {"method": "GET", "header": [], "url": {"raw": "{{ZLMediaKit_URL}}/index/api/stopSendRtp?secret={{ZLMediaKit_secret}}&vhost={{defaultVhost}}&app=live&stream=obs", "host": ["{{ZLMediaKit_URL}}"], "path": ["index", "api", "stopSendRtp"], "query": [{"key": "secret", "value": "{{ZLMediaKit_secret}}", "description": "api操作密钥(配置文件配置)"}, {"key": "vhost", "value": "{{defaultVhost}}", "description": "虚拟主机，例如__defaultVhost__"}, {"key": "app", "value": "live", "description": "应用名，例如 live"}, {"key": "stream", "value": "obs", "description": "流id，例如 obs"}, {"key": "ssrc", "value": "", "description": "根据ssrc关停某路rtp推流，不传时关闭所有推流", "disabled": true}]}}, "response": []}, {"name": "获取rtp发送列表(listRtpSender)", "request": {"method": "GET", "header": [], "url": {"raw": "{{ZLMediaKit_URL}}/index/api/listRtpSender?secret={{ZLMediaKit_secret}}&vhost={{defaultVhost}}&app=live&stream=test", "host": ["{{ZLMediaKit_URL}}"], "path": ["index", "api", "listRtpSender"], "query": [{"key": "secret", "value": "{{ZLMediaKit_secret}}", "description": "api操作密钥(配置文件配置)"}, {"key": "vhost", "value": "{{defaultVhost}}", "description": "虚拟主机，例如__defaultVhost__"}, {"key": "app", "value": "live", "description": "应用名，例如 live"}, {"key": "stream", "value": "test", "description": "流id，例如 obs"}]}}, "response": []}, {"name": "获取版本信息(version)", "request": {"method": "GET", "header": [], "url": {"raw": "{{ZLMediaKit_URL}}/index/api/version?secret={{ZLMediaKit_secret}}", "host": ["{{ZLMediaKit_URL}}"], "path": ["index", "api", "version"], "query": [{"key": "secret", "value": "{{ZLMediaKit_secret}}"}]}}, "response": []}, {"name": "获取拉流代理信息(getProxyInfo)", "request": {"method": "GET", "header": [], "url": {"raw": "{{ZLMediaKit_URL}}/index/api/getProxyInfo?secret={{ZLMediaKit_secret}}&key=__defaultVhost__/live/test", "host": ["{{ZLMediaKit_URL}}"], "path": ["index", "api", "getProxyInfo"], "query": [{"key": "secret", "value": "{{ZLMediaKit_secret}}"}, {"key": "key", "value": "__defaultVhost__/live/test"}]}}, "response": []}, {"name": "获取推流代理信息(getProxyPusherInfo)", "request": {"method": "GET", "header": [], "url": {"raw": "{{ZLMediaKit_URL}}/index/api/getProxyPusherInfo?secret={{ZLMediaKit_secret}}&key=rtmp/__defaultVhost__/live/test/f40a8ab006cac16ecc0858409e890491", "host": ["{{ZLMediaKit_URL}}"], "path": ["index", "api", "getProxyPusherInfo"], "query": [{"key": "secret", "value": "{{ZLMediaKit_secret}}"}, {"key": "key", "value": "rtmp/__defaultVhost__/live/test/f40a8ab006cac16ecc0858409e890491"}]}}, "response": []}, {"name": "点播mp4文件(loadMP4File)", "request": {"method": "GET", "header": [], "url": {"raw": "{{ZLMediaKit_URL}}/index/api/loadMP4File?secret={{ZLMediaKit_secret}}&vhost={{defaultVhost}}&app=live&stream=test&file_path=/path/to/mp4/file.mp4", "host": ["{{ZLMediaKit_URL}}"], "path": ["index", "api", "loadMP4File"], "query": [{"key": "secret", "value": "{{ZLMediaKit_secret}}", "description": "api操作密钥(配置文件配置)"}, {"key": "vhost", "value": "{{defaultVhost}}", "description": "添加的流的虚拟主机，例如__defaultVhost__"}, {"key": "app", "value": "live", "description": "添加的流的应用名，例如live"}, {"key": "stream", "value": "test", "description": "添加的流的id名，例如test"}, {"key": "file_path", "value": "/path/to/mp4/file.mp4", "description": "mp4文件绝对路径"}, {"key": "file_repeat", "value": "1", "description": "是否循环点播mp4文件，如果配置文件已经开启循环点播，此参数无效", "disabled": true}, {"key": "enable_hls", "value": "", "description": "是否转hls-ts", "disabled": true}, {"key": "enable_hls_fmp4", "value": "", "description": "是否转hls-fmp4", "disabled": true}, {"key": "enable_mp4", "value": "", "description": "是否mp4录制，默认不开启(覆盖配置文件)", "disabled": true}, {"key": "enable_rtsp", "value": "1", "description": "是否转协议为rtsp/webrtc", "disabled": true}, {"key": "enable_rtmp", "value": "1", "description": "是否转协议为rtmp/flv", "disabled": true}, {"key": "enable_ts", "value": "1", "description": "是否转协议为http-ts/ws-ts", "disabled": true}, {"key": "enable_fmp4", "value": "1", "description": "是否转协议为http-fmp4/ws-fmp4", "disabled": true}, {"key": "enable_audio", "value": "1", "description": "转协议是否开启音频", "disabled": true}, {"key": "add_mute_audio", "value": "1", "description": "转协议无音频时，是否添加静音aac音频", "disabled": true}, {"key": "mp4_save_path", "value": "", "description": "mp4录制保存根目录，置空使用默认目录", "disabled": true}, {"key": "mp4_max_second", "value": "1800", "description": "mp4录制切片大小，单位秒", "disabled": true}, {"key": "hls_save_path", "value": "", "description": "hls保存根目录，置空使用默认目录", "disabled": true}, {"key": "modify_stamp", "value": "", "description": "是否修改原始时间戳，默认值2；取值范围：0.采用源视频流绝对时间戳，不做任何改变;1.采用zlmediakit接收数据时的系统时间戳(有平滑处理);2.采用源视频流时间戳相对时间戳(增长量)，有做时间戳跳跃和回退矫正", "disabled": true}, {"key": "auto_close", "value": "", "description": "无人观看时，是否直接关闭(而不是通过on_none_reader hook返回close)；强制开启，此参数不生效", "disabled": true}]}}, "response": []}, {"name": "下载文件(downloadFile)", "request": {"method": "GET", "header": [], "url": {"raw": "{{ZLMediaKit_URL}}/index/api/downloadFile?file_path=/path/to/file.ext", "host": ["{{ZLMediaKit_URL}}"], "path": ["index", "api", "downloadFile"], "query": [{"key": "file_path", "value": "/path/to/file.ext", "description": "文件绝对路径，根据文件名生成Content-Type；该接口将触发on_http_access hook"}, {"key": "save_name", "value": "test", "description": "浏览器下载文件后保存文件名；可选参数", "disabled": true}]}}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "ZLMediaKit_URL", "value": "zlmediakit.com:8880"}, {"key": "ZLMediaKit_secret", "value": "035c73f7-bb6b-4889-a715-d9eb2d1925cc"}, {"key": "defaultVhost", "value": "__defaultVhost__"}]}