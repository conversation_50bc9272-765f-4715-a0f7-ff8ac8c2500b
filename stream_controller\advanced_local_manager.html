<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>本地FFmpeg无缝推流控制器</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f8f9fa; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 10px; margin-bottom: 30px; }
        .grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        .panel { background: white; padding: 25px; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
        .panel h3 { color: #333; margin-bottom: 15px; border-bottom: 2px solid #667eea; padding-bottom: 10px; }
        .btn { padding: 12px 24px; margin: 5px; border: none; border-radius: 6px; cursor: pointer; font-size: 16px; transition: all 0.3s; }
        .btn-primary { background: #667eea; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        .btn:hover { transform: translateY(-2px); box-shadow: 0 4px 8px rgba(0,0,0,0.15); }
        .video-container { background: #000; border-radius: 8px; overflow: hidden; margin: 10px 0; }
        .video-player { width: 100%; height: 300px; border-radius: 8px; }
        .status { padding: 15px; background: #f8f9fa; border-radius: 6px; margin: 10px 0; font-family: monospace; }
        .file-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)); gap: 10px; }
        .file-card { border: 1px solid #ddd; padding: 15px; border-radius: 8px; text-align: center; }
        .file-name { font-weight: bold; color: #333; margin-bottom: 10px; }
        .progress-bar { width: 100%; height: 8px; background: #eee; border-radius: 4px; overflow: hidden; }
        .progress-fill { height: 100%; background: linear-gradient(90deg, #667eea, #28a745); transition: width 0.3s; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎬 本地FFmpeg无缝推流控制器</h1>
            <p>零依赖，本地运行，实时切换，前端无感知</p>
        </div>

        <div class="grid">
            <!-- 实时播放控制 -->
            <div class="panel">
                <h3>📺 实时播放控制</h3>
                <div class="video-container">
                    <video id="videoPlayer" class="video-player" controls autoplay muted>
                        <source src="http://127.0.0.1:80/live/stream1/hls.m3u8" type="application/x-mpegURL">
                        <source src="rtmp://127.0.0.1:1935/live/stream1" type="video/mp4">
                    </video>
                </div>
                <div class="controls">
                    <button class="btn btn-primary" onclick="playHLS()">HLS播放</button>
                    <button class="btn btn-success" onclick="playRTMP()">RTMP播放</button>
                    <button class="btn btn-danger" onclick="stopAll()">停止播放</button>
                </div>
                <div class="status" id="status">准备就绪 - 本地ffmpeg运行中</div>
            </div>

            <!-- 文件管理 -->
            <div class="panel">
                <h3>📁 本地文件管理</h3>
                <div id="fileList">
                    <div class="file-card">
                        <div class="file-name">test_video1.mp4</div>
                        <button class="btn btn-primary" onclick="startPush('test_video1.mp4')">开始推流</button>
                        <button class="btn btn-success" onclick="switchVideo('test_video1.mp4')">立即切换</button>
                    </div>
                </div>
            </div>

            <!-- 无缝切换控制 -->
            <div class="panel">
                <h3>🔄 无缝切换控制</h3>
                <div>
                    <label>当前推流文件: <span id="currentFile">test_video1.mp4</span></label>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill" style="width: 0%"></div>
                </div>
                <div class="controls">
                    <button class="btn btn-primary" onclick="createPlaylist()">📋 创建播放列表</button>
                    <button class="btn btn-success" onclick="nextVideo()">⏭️ 下一个文件</button>
                    <button class="btn btn-warning" onclick="switchToVideo()">🔄 立即切换</button>
                    <button class="btn btn-danger" onclick="stopAll()">⏹️ 停止推流</button>
                </div>
            </div>

            <!-- 队列管理 -->
            <div class="panel">
                <h3>📋 播放队列管理</h3>
                <div id="queueContainer">
                    <!-- 队列将动态生成 -->
                </div>
                <div class="controls">
                    <button class="btn btn-primary" onclick="playQueue()">▶️ 播放队列</button>
                    <button class="btn btn-success" onclick="saveQueue()">💾 保存队列</button>
                    <button class="btn btn-danger" onclick="clearQueue()">🗑️ 清空队列</button>
                </div>
            </div>

            <!-- 一键操作 -->
            <div class="panel">
                <h3>🚀 一键操作</h3>
                <div class="controls">
                    <button class="btn btn-primary" onclick="startBatch()">🎬 批量推流</button>
                    <button class="btn btn-success" onclick="monitorStatus()">📊 状态监控</button>
                    <button class="btn btn-info" onclick="openBatchFile()">📁 打开批处理</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        const VIDEO_DIR = 'D:/Dev/ZLMediaKit/ZLMediaKit/Video-files';
        const RTMP_URL = 'rtmp://127.0.0.1:1935/live/stream1';
        const HLS_URL = 'http://127.0.0.1:80/live/stream1/hls.m3u8';
        
        let currentFile = 'test_video1.mp4';
        let playQueue = [];
        let isPlaying = false;

        // 文件管理
        function getFiles() {
            return ['test_video1.mp4', 'video2.mp4', 'video3.mp4'];
        }

        // 推流控制
        function startPush(filename) {
            const fullPath = `${VIDEO_DIR}/${filename}`;
            
            // 创建批处理文件
            const batContent = `
                @echo off
                title 推流:${filename}
                ffmpeg -re -stream_loop -1 -i "${fullPath}" -c:v libx264 -preset veryfast -tune zerolatency -c:a aac -ar 44100 -b:a 128k -pix_fmt yuv420p -g 50 -f flv ${RTMP_URL}
                pause
            `;
            
            // 下载并执行批处理
            const blob = new Blob([batContent], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `push_${filename.replace('.','_')}.bat`;
            a.click();
            
            updateStatus(`正在推流: ${filename}`);
            currentFile = filename;
        }

        function switchVideo(newFile) {
            updateStatus(`正在切换到: ${newFile}...`);
            
            // 创建切换脚本
            const batContent = `
                @echo off
                echo 正在无缝切换到: ${newFile}
                taskkill /f /im ffmpeg.exe
                timeout /t 1 /nobreak
                start "" cmd /k "title 推流:${newFile} & ffmpeg -re -stream_loop -1 -i \"${VIDEO_DIR}/${newFile}\" -c:v libx264 -preset veryfast -tune zerolatency -c:a aac -ar 44100 -b:a 128k -pix_fmt yuv420p -g 50 -f flv ${RTMP_URL}"
                echo 切换完成！
                pause
            `;
            
            const blob = new Blob([batContent], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'switch.bat';
            a.click();
            
            updateStatus(`已切换到: ${newFile}`);
            currentFile = newFile;
        }

        function createPlaylist() {
            const files = getFiles();
            let playlist = '';
            
            files.forEach(file => {
                playlist += `file '${file}'\n`;
            });
            
            const blob = new Blob([playlist], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'playlist.txt';
            a.click();
            
            updateStatus('播放列表已创建');
        }

        function startBatch() {
            const batContent = `
                @echo off
echo === 批量推流控制器 ===
echo.
echo 正在启动队列播放...
echo 使用播放列表: playlist.txt
echo.
echo 按 Ctrl+C 停止播放
echo.
ffmpeg -re -stream_loop -1 -f concat -safe 0 -i playlist.txt -c:v libx264 -preset veryfast -tune zerolatency -f flv ${RTMP_URL}
pause
            `;
            
            const blob = new Blob([batContent], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'batch_play.bat';
            a.click();
        }

        // 播放控制
        function playHLS() {
            const video = document.getElementById('videoPlayer');
            video.src = HLS_URL;
            video.play();
            updateStatus('HLS播放开始');
        }

        function playRTMP() {
            const video = document.getElementById('videoPlayer');
            video.src = RTMP_URL;
            video.play();
            updateStatus('RTMP播放开始');
        }

        function stopAll() {
            const batContent = `
                @echo off
echo 正在停止所有推流...
taskkill /f /im ffmpeg.exe
echo 所有推流已停止
pause
            `;
            
            const blob = new Blob([batContent], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'stop_all.bat';
            a.click();
            
            updateStatus('所有推流已停止');
        }

        function nextVideo() {
            const files = getFiles();
            const currentIndex = files.indexOf(currentFile);
            const nextIndex = (currentIndex + 1) % files.length;
            switchVideo(files[nextIndex]);
        }

        function updateStatus(message) {
            document.getElementById('status').textContent = message;
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadFiles();
            console.log('本地FFmpeg控制器已就绪');
        });
    </script>
</body>
</html>