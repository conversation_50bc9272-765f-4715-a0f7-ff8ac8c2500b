﻿/*
 * Copyright (c) 2016-present The ZLMediaKit project authors. All Rights Reserved.
 *
 * This file is part of ZLMediaKit(https://github.com/ZLMediaKit/ZLMediaKit).
 *
 * Use of this source code is governed by MIT-like license that can be found in the
 * LICENSE file in the root of the source tree. All contributing project authors
 * may be found in the AUTHORS file in the root of the source tree.
 */

#ifndef ZLMEDIAKIT_MK_H264_SPLITTER_H
#define ZLMEDIAKIT_MK_H264_SPLITTER_H

#include "mk_common.h"

#ifdef __cplusplus
extern "C" {
#endif

typedef struct mk_h264_splitter_t *mk_h264_splitter;

/**
 * h264 分帧器输出回调函数
 * @param user_data 设置回调时的用户数据指针
 * @param splitter 对象
 * @param frame 帧数据
 * @param size 帧数据长度
 * h264 frame splitter output callback function
 * @param user_data user data pointer set when setting the callback
 * @param splitter object
 * @param frame frame data
 * @param size frame data length
 
 * [AUTO-TRANSLATED:3e4e4dfa]
 */
typedef void(API_CALL *on_mk_h264_splitter_frame)(void *user_data, mk_h264_splitter splitter, const char *frame, int size);

/**
 * 创建h264分帧器
 * @param cb 分帧回调函数
 * @param user_data 回调用户数据指针
 * @param is_h265 是否是265
 * @return 分帧器对象
 * Create h264 frame splitter
 * @param cb frame splitting callback function
 * @param user_data callback user data pointer
 * @param is_h265 whether it is 265
 * @return frame splitter object
 
 * [AUTO-TRANSLATED:6e06f68d]
 */
API_EXPORT mk_h264_splitter API_CALL mk_h264_splitter_create(on_mk_h264_splitter_frame cb, void *user_data, int is_h265);
API_EXPORT mk_h264_splitter API_CALL mk_h264_splitter_create2(on_mk_h264_splitter_frame cb, void *user_data, on_user_data_free user_data_free, int is_h265);

/**
 * 删除h264分帧器
 * @param ctx 分帧器
 * Delete h264 frame splitter
 * @param ctx frame splitter
 
 * [AUTO-TRANSLATED:e69bb6dd]
 */
API_EXPORT void API_CALL mk_h264_splitter_release(mk_h264_splitter ctx);

/**
 * 输入数据并分帧
 * @param ctx 分帧器
 * @param data h264/h265数据
 * @param size 数据长度
 * Input data and split frames
 * @param ctx frame splitter
 * @param data h264/h265 data
 * @param size data length
 
 
 * [AUTO-TRANSLATED:c6b93aed]
 */
API_EXPORT void API_CALL mk_h264_splitter_input_data(mk_h264_splitter ctx, const char *data, int size);

#ifdef __cplusplus
}
#endif
#endif //ZLMEDIAKIT_MK_H264_SPLITTER_H
