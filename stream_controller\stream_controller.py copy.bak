#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
本地FFmpeg推流控制器
支持Web界面控制、无缝切换、多文件队列
"""

import os
import subprocess
import json
import time
import threading
import re
from collections import deque
from datetime import datetime
from flask import Flask, request, jsonify, render_template_string
from flask_socketio import SocketIO, emit
import psutil

class LocalFFmpegController:
    def __init__(self):
        self.video_dir = r"D:\Dev\ZLMediaKit\ZLMediaKit\Video-files"
        self.rtmp_url = "rtmp://127.0.0.1:1935/live/stream1"
        self.backup_rtmp_url = "rtmp://127.0.0.1:1935/live/stream2"  # 备用推流地址
        self.zlmedia_secret = "035c73f7-bb6b-4889-a715-d9eb2d1925cc"  # ZLMediaKit API密钥
        self.zlmedia_api_url = "http://127.0.0.1:8080/index/api"     # ZLMediaKit API地址
        self.current_process = None
        self.backup_process = None  # 备用推流进程
        self.current_file = None
        self.backup_file = None  # 备用文件
        self.play_queue = []
        self.is_playing = False
        self.queue_thread = None
        
        # 监控相关属性
        self.monitor_thread = None
        self.is_monitoring = False
        self.ffmpeg_logs = deque(maxlen=1000)  # 保存最近1000条日志
        self.performance_data = deque(maxlen=100)  # 保存最近100个性能数据点
        self.start_time = None
        self.frame_count = 0
        self.bitrate_history = deque(maxlen=50)
    
    def fix_file_path(self, filepath):
        """修复文件路径格式问题"""
        if not filepath:
            return None
            
        print(f"原始路径: {repr(filepath)}")
        
        # 处理路径中可能的编码问题
        if isinstance(filepath, bytes):
            filepath = filepath.decode('utf-8', errors='ignore')
        
        # 修复可能的路径分隔符问题
        # 从错误信息看，路径变成了 "D:DevZLMediaKitZLMediaKitVideo-files	est_video1.mp4"
        # 可能是反斜杠被转义或丢失了
        
        # 尝试修复缺失的路径分隔符
        if 'D:Dev' in filepath and 'ZLMediaKit' in filepath:
            # 这是我们预期的路径格式，需要修复分隔符
            fixed_path = filepath.replace('D:Dev', 'D:\\Dev')
            fixed_path = fixed_path.replace('ZLMediaKitZLMediaKit', 'ZLMediaKit\\ZLMediaKit')
            fixed_path = fixed_path.replace('Video-files\t', 'Video-files\\')
            fixed_path = fixed_path.replace('\t', '\\')
            print(f"修复后路径: {repr(fixed_path)}")
            filepath = fixed_path
        
        # 标准化路径
        filepath = os.path.normpath(filepath)
        print(f"标准化路径: {repr(filepath)}")
        
        return filepath
        
    def get_video_files(self):
        """获取可用视频文件"""
        files = []
        print(f"扫描视频目录: {self.video_dir}")
        
        for ext in ['*.mp4', '*.avi', '*.mkv', '*.mov']:
            import glob
            pattern = os.path.join(self.video_dir, ext)
            found_files = glob.glob(pattern)
            print(f"模式 {pattern} 找到文件: {found_files}")
            files.extend(found_files)
        
        result = [{
            'name': os.path.basename(f),
            'path': f,
            'size': os.path.getsize(f)
        } for f in files]
        
        print(f"返回文件列表: {result}")
        return result
    
    def analyze_video_and_warmup(self, filepath):
        """
        分析视频内容并预热编码器
        """
        try:
            # 使用ffprobe分析视频信息
            probe_cmd = [
                'ffprobe', '-v', 'quiet', '-print_format', 'json',
                '-show_format', '-show_streams', filepath
            ]
            
            result = subprocess.run(
                probe_cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0:
                probe_data = json.loads(result.stdout)
                
                # 提取视频信息
                video_stream = None
                audio_stream = None
                for stream in probe_data.get('streams', []):
                    if stream.get('codec_type') == 'video':
                        video_stream = stream
                    elif stream.get('codec_type') == 'audio':
                        audio_stream = stream
                
                video_info = {}
                if video_stream:
                    # 提取视频信息
                    video_info.update({
                        'width': int(video_stream.get('width', 1920)),
                        'height': int(video_stream.get('height', 1080)),
                        'codec_name': video_stream.get('codec_name', 'h264'),
                        'avg_frame_rate': video_stream.get('avg_frame_rate', '30/1')
                    })
                
                if audio_stream:
                    # 提取音频信息
                    video_info.update({
                        'audio_codec': audio_stream.get('codec_name', 'aac'),
                        'sample_rate': int(audio_stream.get('sample_rate', 44100)),
                        'channels': int(audio_stream.get('channels', 2))
                    })
                
                # 根据视频信息决定编码预设
                if video_info.get('width', 1920) > 1920 or video_info.get('height', 1080) > 1080:
                    video_info['preset'] = 'faster'  # 高分辨率使用更快的预设
                else:
                    video_info['preset'] = 'veryfast'  # 普通分辨率使用极快预设
                
                print(f"视频分析完成: {video_info}")
                return video_info
        except subprocess.TimeoutExpired:
            print("视频分析超时")
        except Exception as e:
            print(f"视频分析失败: {e}")
        
        # 默认配置
        return {
            'width': 1920,
            'height': 1080,
            'preset': 'veryfast',
            'avg_frame_rate': '30/1'
        }
    
    def start_push(self, filepath, rtmp_url=None, video_info=None):
        """开始推流单个文件"""
        if not rtmp_url:
            rtmp_url = self.rtmp_url
            
        # 检查是否已有相同RTMP URL的推流在运行
        if rtmp_url == self.rtmp_url and self.current_process and self.current_process.poll() is None:
            return {'code': -1, 'msg': '主推流已在运行'}
        elif rtmp_url == self.backup_rtmp_url and self.backup_process and self.backup_process.poll() is None:
            return {'code': -1, 'msg': '备用推流已在运行'}
        
        # 修复文件路径
        filepath = self.fix_file_path(filepath)
        
        if not os.path.exists(filepath):
            # 尝试修复路径问题
            if not os.path.isabs(filepath):
                # 如果是相对路径，与视频目录拼接
                full_path = os.path.join(self.video_dir, filepath)
                full_path = os.path.normpath(full_path)
                print(f"尝试完整路径: {full_path}")
                if os.path.exists(full_path):
                    filepath = full_path
                else:
                    return {'code': -2, 'msg': f'文件不存在: {filepath}'}
            else:
                return {'code': -2, 'msg': f'文件不存在: {filepath}'}
        
        try:
            # 如果没有提供视频信息，则分析视频
            if video_info is None:
                video_info = self.analyze_video_and_warmup(filepath)
            
            # 构建FFmpeg命令，添加同步和优化参数
            cmd = [
                'ffmpeg', '-re', '-stream_loop', '-1',
                '-i', filepath,
                '-copyts',  # 保持时间戳
                '-vsync', 'cfr',  # 恒定帧率
                '-muxdelay', '0',  # 减少复用器延迟
                '-flvflags', 'no_duration_filesize',  # 优化FLV封装
                '-c:v', 'libx264',
                '-preset', video_info.get('preset', 'veryfast'),
                '-tune', 'zerolatency',
                '-c:a', 'aac', '-ar', str(video_info.get('sample_rate', 44100)), 
                '-b:a', '128k',
                '-pix_fmt', 'yuv420p', '-g', '50',
                '-f', 'flv', rtmp_url
            ]
            
            print(f"启动推流，命令: {' '.join(cmd)}")
            
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                creationflags=subprocess.CREATE_NEW_PROCESS_GROUP
            )
            
            # 根据RTMP URL设置对应的进程和文件
            if rtmp_url == self.rtmp_url:
                self.current_process = process
                self.current_file = filepath
            else:
                self.backup_process = process
                self.backup_file = filepath
                
            # 如果是主推流，启动监控
            if rtmp_url == self.rtmp_url:
                self.start_time = time.time()
                self.frame_count = 0
                self.start_monitoring()
            
            return {'code': 0, 'msg': '推流开始', 'file': os.path.basename(filepath)}
        except Exception as e:
            return {'code': -3, 'msg': str(e)}
    
    def stop_push(self, target="main"):
        """停止推流
        target: "main" - 停止主推流, "backup" - 停止备用推流, "all" - 停止所有推流
        """
        stopped = []
        
        if target in ["main", "all"]:
            if self.current_process and self.current_process.poll() is None:
                try:
                    # 只有停止主推流时才停止监控
                    if target == "main":
                        self.stop_monitoring()
                    
                    self.current_process.terminate()
                    self.current_process.wait(timeout=5)
                    self.current_process = None
                    self.current_file = None
                    if target == "main":
                        self.start_time = None
                    stopped.append("主推流")
                except Exception as e:
                    return {'code': -1, 'msg': f'停止主推流失败: {str(e)}'}
        
        if target in ["backup", "all"]:
            if self.backup_process and self.backup_process.poll() is None:
                try:
                    self.backup_process.terminate()
                    self.backup_process.wait(timeout=5)
                    self.backup_process = None
                    self.backup_file = None
                    stopped.append("备用推流")
                except Exception as e:
                    return {'code': -1, 'msg': f'停止备用推流失败: {str(e)}'}
        
        if stopped:
            return {'code': 0, 'msg': f'已停止: {", ".join(stopped)}'}
        return {'code': 0, 'msg': '无运行中的推流'}
    
    def switch_zlmedia_source(self, main_stream, backup_stream):
        """
        调用ZLMediaKit API切换主备源
        """
        try:
            import requests
            
            # 设置主备源配置
            config_data = {
                "secret": self.zlmedia_secret,
                "main_stream": main_stream,
                "backup_stream": backup_stream
            }
            
            response = requests.post(
                f"{self.zlmedia_api_url}/setServerConfig",
                json=config_data,
                timeout=5
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get("code", -1) == 0:
                    print("ZLMediaKit主备源配置成功")
                    return True
                else:
                    print(f"ZLMediaKit配置失败: {result.get('msg', '未知错误')}")
            else:
                print(f"ZLMediaKit API请求失败: {response.status_code}")
        except ImportError:
            print("未安装requests库，跳过ZLMediaKit API调用")
        except Exception as e:
            print(f"ZLMediaKit API调用失败: {e}")
        
        return False
    
    def switch_video(self, new_filepath):
        """无缝切换视频 - 改进版实现"""
        # 修复文件路径
        new_filepath = self.fix_file_path(new_filepath)
        
        if not os.path.exists(new_filepath):
            # 尝试修复路径问题
            if not os.path.isabs(new_filepath):
                # 如果是相对路径，与视频目录拼接
                full_path = os.path.join(self.video_dir, new_filepath)
                full_path = os.path.normpath(full_path)
                print(f"尝试完整路径: {full_path}")
                if os.path.exists(full_path):
                    new_filepath = full_path
                else:
                    return {'code': -1, 'msg': f'文件不存在: {new_filepath}'}
            else:
                return {'code': -1, 'msg': f'文件不存在: {new_filepath}'}
        
        try:
            # 1. 分析视频内容并预热编码器
            video_info = self.analyze_video_and_warmup(new_filepath)
            
            # 2. 首先启动备用推流（推送到备用地址）
            backup_result = self.start_push(new_filepath, self.backup_rtmp_url, video_info)
            if backup_result['code'] != 0:
                return {'code': -2, 'msg': f'启动备用推流失败: {backup_result["msg"]}'}
            
            # 3. 等待备用推流稳定（很短的时间，比如0.1秒）
            time.sleep(0.1)
            
            # 4. 调用ZLMediaKit API切换主备源（如果API调用失败，则使用原有方式）
            if self.switch_zlmedia_source(self.backup_rtmp_url, self.rtmp_url):
                # API调用成功，ZLMediaKit已切换主备源
                print("通过ZLMediaKit API完成主备源切换")
                
                # 5. 停止之前的主推流
                stop_result = self.stop_push("main")
                if stop_result['code'] != 0:
                    print(f"停止主推流警告: {stop_result['msg']}")
                
                # 6. 将备用推流提升为主推流
                self.current_process = self.backup_process
                self.current_file = self.backup_file
                self.backup_process = None
                self.backup_file = None
                
                return {'code': 0, 'msg': '通过ZLMediaKit API实现无缝切换完成', 'file': os.path.basename(new_filepath)}
            else:
                # API调用失败，回退到原有的切换方式
                print("ZLMediaKit API调用失败，使用原有切换方式")
                
                # 停止当前主推流
                stop_result = self.stop_push("main")
                if stop_result['code'] != 0:
                    # 如果停止主推流失败，也要停止备用推流
                    self.stop_push("backup")
                    return stop_result
                
                # 将备用推流提升为主推流
                self.current_process = self.backup_process
                self.current_file = self.backup_file
                self.backup_process = None
                self.backup_file = None
                
                return {'code': 0, 'msg': '本地无缝切换完成', 'file': os.path.basename(new_filepath)}
        except Exception as e:
            # 出现异常时清理所有推流
            self.stop_push("all")
            return {'code': -3, 'msg': f'切换失败: {str(e)}'}
    
    def get_status(self):
        """获取当前状态"""
        main_status = None
        backup_status = None
        
        if self.current_process and self.current_process.poll() is None:
            main_status = {
                'is_running': True,
                'current_file': self.current_file,
                'pid': self.current_process.pid
            }
        
        if self.backup_process and self.backup_process.poll() is None:
            backup_status = {
                'is_running': True,
                'current_file': self.backup_file,
                'pid': self.backup_process.pid
            }
        
        return {
            'code': 0,
            'main': main_status,
            'backup': backup_status
        }
    
    def kill_all_ffmpeg(self):
        """强制停止所有ffmpeg进程"""
        try:
            for proc in psutil.process_iter(['pid', 'name']):
                if proc.info['name'] and 'ffmpeg' in proc.info['name'].lower():
                    proc.kill()
            # 清理内部状态
            self.current_process = None
            self.backup_process = None
            self.current_file = None
            self.backup_file = None
            self.start_time = None
            return {'code': 0, 'msg': '所有ffmpeg进程已终止'}
        except Exception as e:
            return {'code': -1, 'msg': str(e)}
    
    def play_queue(self, files):
        """播放队列"""
        if self.is_playing:
            return {'code': -1, 'msg': '队列已在播放中'}
        
        self.play_queue = files
        self.is_playing = True
        
        def play_worker():
            for filepath in self.play_queue:
                if not self.is_playing:
                    break
                
                print(f"正在播放: {os.path.basename(filepath)}")
                result = self.switch_video(filepath)
                if result['code'] != 0:
                    print(f"播放失败: {result['msg']}")
                    break
                
                time.sleep(30)  # 播放30秒后切换下一个
            
            self.is_playing = False
        
        self.queue_thread = threading.Thread(target=play_worker)
        self.queue_thread.start()
        
        return {'code': 0, 'msg': '队列播放开始'}
    
    def start_monitoring(self):
        """开始监控FFmpeg进程"""
        if self.is_monitoring:
            return
        
        self.is_monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_worker)
        self.monitor_thread.daemon = True
        self.monitor_thread.start()
    
    def stop_monitoring(self):
        """停止监控"""
        self.is_monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=1)
    
    def _monitor_worker(self):
        """监控工作线程"""
        while self.is_monitoring:
            try:
                if self.current_process and self.current_process.poll() is None:
                    # 获取进程性能数据
                    process = psutil.Process(self.current_process.pid)
                    cpu_percent = process.cpu_percent()
                    memory_info = process.memory_info()
                    memory_mb = memory_info.rss / 1024 / 1024
                    
                    # 读取FFmpeg输出
                    self._read_ffmpeg_output()
                    
                    # 保存性能数据
                    perf_data = {
                        'timestamp': datetime.now().isoformat(),
                        'cpu_percent': cpu_percent,
                        'memory_mb': memory_mb,
                        'frame_count': self.frame_count,
                        'uptime': time.time() - self.start_time if self.start_time else 0
                    }
                    self.performance_data.append(perf_data)
                    
                    # 通过WebSocket发送实时数据
                    if hasattr(self, 'socketio'):
                        self.socketio.emit('monitor_update', perf_data)
                
                time.sleep(1)  # 每秒更新一次
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                # 进程已结束或无权限访问
                break
            except Exception as e:
                print(f"监控错误: {e}")
                break
    
    def _read_ffmpeg_output(self):
        """读取FFmpeg输出并解析"""
        if not self.current_process:
            return
        
        try:
            # 非阻塞读取stderr（FFmpeg输出到stderr）
            import select
            if hasattr(select, 'select'):
                ready, _, _ = select.select([self.current_process.stderr], [], [], 0)
                if ready:
                    line = self.current_process.stderr.readline()
                    if line:
                        self._parse_ffmpeg_log(line.decode('utf-8', errors='ignore'))
        except:
            # Windows下select不支持管道，使用其他方法
            pass
    
    def _parse_ffmpeg_log(self, line):
        """解析FFmpeg日志行"""
        timestamp = datetime.now().isoformat()
        log_entry = {
            'timestamp': timestamp,
            'content': line.strip(),
            'type': 'info'
        }
        
        # 解析帧数
        frame_match = re.search(r'frame=\s*(\d+)', line)
        if frame_match:
            self.frame_count = int(frame_match.group(1))
        
        # 解析比特率
        bitrate_match = re.search(r'bitrate=\s*([\d.]+)kbits/s', line)
        if bitrate_match:
            bitrate = float(bitrate_match.group(1))
            self.bitrate_history.append({
                'timestamp': timestamp,
                'bitrate': bitrate
            })
        
        # 检测错误
        if 'error' in line.lower() or 'failed' in line.lower():
            log_entry['type'] = 'error'
        elif 'warning' in line.lower():
            log_entry['type'] = 'warning'
        
        self.ffmpeg_logs.append(log_entry)
    
    def get_monitor_data(self):
        """获取监控数据"""
        current_status = self.get_status()
        
        # 获取最新性能数据
        latest_perf = list(self.performance_data)[-20:] if self.performance_data else []
        
        # 获取最新日志
        latest_logs = list(self.ffmpeg_logs)[-50:] if self.ffmpeg_logs else []
        
        # 获取比特率历史
        latest_bitrates = list(self.bitrate_history)[-20:] if self.bitrate_history else []
        
        return {
            'status': current_status,
            'performance': latest_perf,
            'logs': latest_logs,
            'bitrates': latest_bitrates,
            'summary': {
                'total_logs': len(self.ffmpeg_logs),
                'error_count': sum(1 for log in self.ffmpeg_logs if log.get('type') == 'error'),
                'warning_count': sum(1 for log in self.ffmpeg_logs if log.get('type') == 'warning'),
                'current_frame': self.frame_count,
                'uptime': time.time() - self.start_time if self.start_time else 0
            }
        }
    
    def clear_monitor_data(self):
        """清空监控数据"""
        self.ffmpeg_logs.clear()
        self.performance_data.clear()
        self.bitrate_history.clear()
        self.frame_count = 0
        return {'code': 0, 'msg': '监控数据已清空'}

# Flask Web应用
app = Flask(__name__)
app.config['SECRET_KEY'] = 'ffmpeg_monitor_secret_key'
socketio = SocketIO(app, cors_allowed_origins="*")

# 创建控制器实例
controller = LocalFFmpegController()

# 注册控制器到自身，用于WebSocket回调
controller.socketio = socketio

# 设置路由
@app.route('/')
def index():
    """主页面"""
    import os
    index_file = os.path.join(os.path.dirname(__file__), 'advanced_local_manager.html')
    if os.path.exists(index_file):
        with open(index_file, 'r', encoding='utf-8') as f:
            return f.read()
    else:
        return "页面文件未找到", 404

@app.route('/api/files')
def api_files():
    return jsonify(controller.get_video_files())

@app.route('/api/start', methods=['POST'])
def api_start():
    filepath = request.json.get('path')
    return jsonify(controller.start_push(filepath))

@app.route('/api/stop', methods=['POST'])
def api_stop():
    target = request.json.get('target', 'main')
    return jsonify(controller.stop_push(target))

@app.route('/api/switch', methods=['POST'])
def api_switch():
    filepath = request.json.get('path')
    return jsonify(controller.switch_video(filepath))

@app.route('/api/status')
def api_status():
    return jsonify(controller.get_status())

@app.route('/api/kill-all', methods=['POST'])
def api_kill_all():
    return jsonify(controller.kill_all_ffmpeg())

@app.route('/api/queue/play', methods=['POST'])
def api_queue_play():
    files = request.json.get('files', [])
    return jsonify(controller.play_queue(files))

@app.route('/api/queue/stop', methods=['POST'])
def api_queue_stop():
    controller.is_playing = False
    return jsonify({'code': 0, 'msg': '队列播放已停止'})

# 监控相关API
@app.route('/api/monitor/data')
def api_monitor_data():
    return jsonify(controller.get_monitor_data())

@app.route('/api/monitor/clear', methods=['POST'])
def api_monitor_clear():
    return jsonify(controller.clear_monitor_data())

@app.route('/api/monitor/start', methods=['POST'])
def api_monitor_start():
    controller.start_monitoring()
    return jsonify({'code': 0, 'msg': '监控已启动'})

@app.route('/api/monitor/stop', methods=['POST'])
def api_monitor_stop():
    controller.stop_monitoring()
    return jsonify({'code': 0, 'msg': '监控已停止'})

@app.route('/monitor')
def monitor_page():
    return render_template_string(MONITOR_HTML_TEMPLATE)

@app.route('/test')
def test_webrtc_page():
    """WebRTC播放器测试页面"""
    import os
    test_file = os.path.join(os.path.dirname(__file__), 'test_webrtc.html')
    with open(test_file, 'r', encoding='utf-8') as f:
        return f.read()

@app.route('/static/<path:filename>')
def static_files(filename):
    """提供静态文件服务"""
    import os
    from flask import send_from_directory
    static_dir = os.path.join(os.path.dirname(__file__), '.')
    return send_from_directory(static_dir, filename)

# WebRTC相关API
@app.route('/api/webrtc/play', methods=['POST'])
def api_webrtc_play():
    """获取WebRTC播放配置"""
    stream_id = request.json.get('stream', 'stream1')
    
    # ZLMediaKit WebRTC播放配置
    webrtc_config = {
        'api_url': 'http://127.0.0.1:8080/index/api/webrtc',
        'stream_url': f'http://127.0.0.1:8080/live/{stream_id}/hls.m3u8',
        'webrtc_url': f'webrtc://127.0.0.1:8080/live/{stream_id}',
        'flv_url': f'http://127.0.0.1:8080/live/{stream_id}.live.flv',
        'stream_id': stream_id,
        'app': 'live',
        'vhost': '__defaultVhost__'
    }
    
    return jsonify({
        'code': 0,
        'data': webrtc_config,
        'msg': 'WebRTC配置获取成功'
    })

@app.route('/api/stream/info')
def api_stream_info():
    """获取流信息"""
    try:
        import requests
        # 调用ZLMediaKit API获取流信息
        response = requests.get('http://127.0.0.1:8080/index/api/getMediaList', timeout=5)
        if response.status_code == 200:
            return jsonify(response.json())
        else:
            return jsonify({'code': -1, 'msg': '无法获取流信息'})
    except Exception as e:
        return jsonify({'code': -1, 'msg': f'获取流信息失败: {str(e)}'})

@app.route('/api/player/stats', methods=['POST'])
def api_player_stats():
    """获取播放器统计信息"""
    stats = request.json
    # 可以在这里处理播放器统计信息
    print(f"播放器统计: {stats}")
    return jsonify({'code': 0, 'msg': '统计信息已接收'})

# WebSocket事件处理
@socketio.on('connect')
def handle_connect():
    print('客户端已连接')
    emit('connection_status', {'status': 'connected'})

@socketio.on('disconnect')
def handle_disconnect():
    print('客户端已断开连接')

@socketio.on('request_monitor_data')
def handle_monitor_data_request():
    """处理监控数据请求"""
    data = controller.get_monitor_data()
    emit('monitor_data', data)

# 监控页面HTML模板
MONITOR_HTML_TEMPLATE = '''
<!-- FFmpeg监控面板 -->
<!DOCTYPE html>
<html>
<head>
    <title>FFmpeg推流监控面板</title>
    <meta charset="utf-8">
    <script src="https://cdn.socket.io/4.0.0/socket.io.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 1200px; margin: 0 auto; }
        .dashboard { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .card { border: 1px solid #ddd; border-radius: 8px; padding: 15px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .chart-container { height: 200px; margin-top: 10px; }
        .status-indicator { display: inline-block; width: 12px; height: 12px; border-radius: 50%; margin-right: 5px; }
        .status-running { background-color: #4CAF50; }
        .status-stopped { background-color: #f44336; }
        .logs { height: 200px; overflow-y: auto; background: #f5f5f5; padding: 10px; font-family: monospace; font-size: 12px; }
        .log-error { color: #f44336; }
        .log-warning { color: #ff9800; }
        .controls { margin: 20px 0; }
        button { padding: 10px 15px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; }
        .btn-primary { background-color: #2196F3; color: white; }
        .btn-danger { background-color: #f44336; color: white; }
        .btn-success { background-color: #4CAF50; color: white; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎬 FFmpeg推流监控面板</h1>
        
        <div class="controls">
            <button class="btn-primary" onclick="startMonitor()">开始监控</button>
            <button class="btn-danger" onclick="stopMonitor()">停止监控</button>
            <button class="btn-success" onclick="clearData()">清空数据</button>
        </div>
        
        <div class="dashboard">
            <div class="card">
                <h3>📊 推流状态</h3>
                <div id="streamStatus">加载中...</div>
            </div>
            
            <div class="card">
                <h3>📈 CPU使用率</h3>
                <div class="chart-container">
                    <canvas id="cpuChart"></canvas>
                </div>
            </div>
            
            <div class="card">
                <h3>💾 内存使用</h3>
                <div class="chart-container">
                    <canvas id="memoryChart"></canvas>
                </div>
            </div>
            
            <div class="card">
                <h3>📶 比特率</h3>
                <div class="chart-container">
                    <canvas id="bitrateChart"></canvas>
                </div>
            </div>
        </div>
        
        <div class="card">
            <h3>📝 实时日志</h3>
            <div id="logs" class="logs"></div>
        </div>
    </div>

    <script>
        // 初始化Socket.IO连接
        const socket = io();
        
        // 初始化图表
        const cpuChartCtx = document.getElementById('cpuChart').getContext('2d');
        const memoryChartCtx = document.getElementById('memoryChart').getContext('2d');
        const bitrateChartCtx = document.getElementById('bitrateChart').getContext('2d');
        
        const cpuChart = new Chart(cpuChartCtx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: 'CPU使用率 (%)',
                    data: [],
                    borderColor: 'rgb(75, 192, 192)',
                    tension: 0.1
                }]
            },
            options: { responsive: true, maintainAspectRatio: false }
        });
        
        const memoryChart = new Chart(memoryChartCtx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: '内存使用 (MB)',
                    data: [],
                    borderColor: 'rgb(255, 99, 132)',
                    tension: 0.1
                }]
            },
            options: { responsive: true, maintainAspectRatio: false }
        });
        
        const bitrateChart = new Chart(bitrateChartCtx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: '比特率 (kbps)',
                    data: [],
                    borderColor: 'rgb(54, 162, 235)',
                    tension: 0.1
                }]
            },
            options: { responsive: true, maintainAspectRatio: false }
        });
        
        // 连接事件
        socket.on('connect', function() {
            console.log('已连接到服务器');
            socket.emit('request_monitor_data');
        });
        
        // 接收监控更新
        socket.on('monitor_update', function(data) {
            updateCharts(data);
        });
        
        // 接收完整监控数据
        socket.on('monitor_data', function(data) {
            updateDashboard(data);
        });
        
        // 更新图表
        function updateCharts(data) {
            const now = new Date(data.timestamp).toLocaleTimeString();
            
            // 更新CPU图表
            cpuChart.data.labels.push(now);
            cpuChart.data.datasets[0].data.push(data.cpu_percent);
            if (cpuChart.data.labels.length > 20) {
                cpuChart.data.labels.shift();
                cpuChart.data.datasets[0].data.shift();
            }
            cpuChart.update();
            
            // 更新内存图表
            memoryChart.data.labels.push(now);
            memoryChart.data.datasets[0].data.push(data.memory_mb);
            if (memoryChart.data.labels.length > 20) {
                memoryChart.data.labels.shift();
                memoryChart.data.datasets[0].data.shift();
            }
            memoryChart.update();
        }
        
        // 更新仪表板
        function updateDashboard(data) {
            // 更新状态
            const statusEl = document.getElementById('streamStatus');
            if (data.status.main && data.status.main.is_running) {
                statusEl.innerHTML = `
                    <p><span class="status-indicator status-running"></span>主推流运行中</p>
                    <p>文件: ${data.status.main.current_file || '未知'}</p>
                    <p>PID: ${data.status.main.pid || '未知'}</p>
                    <p>运行时间: ${Math.floor(data.summary.uptime || 0)} 秒</p>
                `;
            } else {
                statusEl.innerHTML = '<p><span class="status-indicator status-stopped"></span>推流已停止</p>';
            }
            
            // 更新日志
            const logsEl = document.getElementById('logs');
            logsEl.innerHTML = '';
            data.logs.forEach(log => {
                const logEl = document.createElement('div');
                logEl.textContent = `[${new Date(log.timestamp).toLocaleTimeString()}] ${log.content}`;
                if (log.type === 'error') {
                    logEl.className = 'log-error';
                } else if (log.type === 'warning') {
                    logEl.className = 'log-warning';
                }
                logsEl.appendChild(logEl);
            });
            logsEl.scrollTop = logsEl.scrollHeight;
            
            // 更新比特率图表
            if (data.bitrates.length > 0) {
                const lastBitrate = data.bitrates[data.bitrates.length - 1];
                const time = new Date(lastBitrate.timestamp).toLocaleTimeString();
                bitrateChart.data.labels.push(time);
                bitrateChart.data.datasets[0].data.push(lastBitrate.bitrate);
                if (bitrateChart.data.labels.length > 20) {
                    bitrateChart.data.labels.shift();
                    bitrateChart.data.datasets[0].data.shift();
                }
                bitrateChart.update();
            }
        }
        
        // 控制函数
        function startMonitor() {
            fetch('/api/monitor/start', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    alert(data.msg);
                    if (data.code === 0) {
                        socket.emit('request_monitor_data');
                    }
                });
        }
        
        function stopMonitor() {
            fetch('/api/monitor/stop', { method: 'POST' })
                .then(response => response.json())
                .then(data => alert(data.msg));
        }
        
        function clearData() {
            fetch('/api/monitor/clear', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    alert(data.msg);
                    if (data.code === 0) {
                        // 清空图表
                        cpuChart.data.labels = [];
                        cpuChart.data.datasets[0].data = [];
                        cpuChart.update();
                        
                        memoryChart.data.labels = [];
                        memoryChart.data.datasets[0].data = [];
                        memoryChart.update();
                        
                        bitrateChart.data.labels = [];
                        bitrateChart.data.datasets[0].data = [];
                        bitrateChart.update();
                        
                        // 清空日志
                        document.getElementById('logs').innerHTML = '';
                    }
                });
        }
        
        // 定期请求数据更新
        setInterval(() => {
            socket.emit('request_monitor_data');
        }, 2000);
    </script>
</body>
</html>
'''

if __name__ == '__main__':
    import argparse
    parser = argparse.ArgumentParser(description='FFmpeg推流控制器')
    parser.add_argument('--debug', action='store_true', help='启用调试模式')
    parser.add_argument('--port', type=int, default=5000, help='端口号 (默认: 5000)')
    parser.add_argument('--host', default='0.0.0.0', help='主机地址 (默认: 0.0.0.0)')
    
    args = parser.parse_args()
    
    print("=" * 60)
    print("🎬 本地FFmpeg推流控制器")
    print("=" * 60)
    print(f"📡 服务地址: http://127.0.0.1:{args.port}")
    print(f"🌐 网络地址: http://{args.host}:{args.port}")
    print(f"📁 视频目录: {controller.video_dir}")
    print(f"📺 主推流地址: {controller.rtmp_url}")
    print(f"📺 备用推流地址: {controller.backup_rtmp_url}")
    print("=" * 60)
    
    try:
        if args.debug:
            print("⚠️  调试模式已启用（关闭自动重载以避免模块导入问题）")
            socketio.run(
                app,
                host=args.host, 
                port=args.port, 
                debug=True, 
                use_reloader=False  # 关闭自动重载
            )
        else:
            print("✅ 生产模式启动")
            socketio.run(
                app,
                host=args.host, 
                port=args.port, 
                debug=False
            )
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")
        controller.stop_push("all")  # 停止所有推流
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        controller.stop_push("all")  # 停止所有推流

            playerStats = {
                protocol: currentProtocol.toUpperCase(),
                url: '',
                resolution: '',
                fps: 0,
                bitrate: 0,
                bufferHealth: 0,
                errors: 0
            };
            updatePlayerStatsDisplay();
        }
        
        async function refreshPlayer() {
            if (currentPlayer) {
                await stopPlay();
                setTimeout(async () => await startPlay(), 1000);
            }
        }
        
        function toggleMute() {
            const video = document.getElementById('videoPlayer');
            video.muted = !video.muted;
            document.querySelector('[onclick="toggleMute()"]').textContent = 
                video.muted ? '🔇 取消静音' : '🔊 静音';
        }
        
        function updateStreamStatus(status) {
            const statusEl = document.getElementById('streamStatus');
            statusEl.textContent = status === 'online' ? '在线' : '离线';
            statusEl.className = `stream-status stream-${status}`;
        }
        
        function updatePlayerStats() {
            if (!currentPlayer) return;
            
            const video = document.getElementById('videoPlayer');
            
            if (video.videoWidth && video.videoHeight) {
                playerStats.resolution = `${video.videoWidth}x${video.videoHeight}`;
            }
            
            if (video.buffered.length > 0) {
                const buffered = video.buffered.end(video.buffered.length - 1);
                const currentTime = video.currentTime;
                playerStats.bufferHealth = Math.max(0, buffered - currentTime);
            }
            
            updatePlayerStatsDisplay();
        }
        
        function updatePlayerStatsDisplay() {
            const statsEl = document.getElementById('playerStats');
            statsEl.innerHTML = `
                协议: ${playerStats.protocol}<br>
                URL: ${playerStats.url}<br>
                分辨率: ${playerStats.resolution || '未知'}<br>
                帧率: ${playerStats.fps.toFixed(1)} FPS<br>
                比特率: ${playerStats.bitrate.toFixed(0)} kbps<br>
                缓冲: ${playerStats.bufferHealth.toFixed(1)}s<br>
                错误: ${playerStats.errors}
            `;
        }
        
        function toggleDebug() {
            const debugDiv = document.getElementById('playerDebug');
            debugDiv.style.display = debugDiv.style.display === 'none' ? 'block' : 'none';
            
            if (debugDiv.style.display === 'block') {
                updateDebugInfo();
            }
        }
        
        function updateDebugInfo() {
            const debugInfo = document.getElementById('debugInfo');
            const info = [];
            
            info.push(`WebRTC播放器类: ${!!window.ZLMWebRTCPlayer ? '✅' : '❌'}`);
            info.push(`HLS.js: ${!!window.Hls ? '✅' : '❌'}`);
            info.push(`FLV.js: ${!!window.flvjs ? '✅' : '❌'}`);
            info.push(`WebRTC支持: ${!!window.RTCPeerConnection ? '✅' : '❌'}`);
            info.push(`当前协议: ${currentProtocol}`);
            info.push(`播放器实例: ${currentPlayer ? '有' : '无'}`);
            info.push(`浏览器: ${navigator.userAgent.split(' ')[0]}`);
            
            debugInfo.innerHTML = info.join('<br>');
        }
    </script>
</body>
</html>
'''

MONITOR_HTML_TEMPLATE = '''
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FFmpeg实时监控面板</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/hls.js@latest"></script>
    <script src="https://unpkg.com/flv.js/dist/flv.min.js"></script>
    <script src="/static/webrtc_player.js"></script>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: #333;
            min-height: 100vh;
        }
        .header {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            padding: 20px;
            color: white;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.2);
        }
        .header h1 { font-size: 2.5em; margin-bottom: 10px; }
        .header p { opacity: 0.9; font-size: 1.1em; }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .panel {
            background: rgba(255,255,255,0.95);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            backdrop-filter: blur(10px);
        }
        .panel h3 {
            color: #2a5298;
            margin-bottom: 20px;
            font-size: 1.3em;
            border-bottom: 2px solid #2a5298;
            padding-bottom: 10px;
        }
        .full-width { grid-column: 1 / -1; }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        .status-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }
        .status-card h4 { font-size: 0.9em; opacity: 0.9; margin-bottom: 10px; }
        .status-card .value { font-size: 2em; font-weight: bold; }
        .status-card .unit { font-size: 0.8em; opacity: 0.8; }
        .chart-container {
            position: relative;
            height: 300px;
            margin: 20px 0;
        }
        .log-container {
            height: 400px;
            overflow-y: auto;
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        .log-entry {
            margin-bottom: 5px;
            padding: 5px;
            border-radius: 3px;
        }
        .log-info { background: #e8f4fd; color: #0c5460; }
        .log-warning { background: #fff3cd; color: #856404; }
        .log-error { background: #f8d7da; color: #721c24; }
        .log-timestamp { color: #6c757d; font-size: 11px; }
        .btn {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
        }
        .btn-primary { background: #2a5298; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        .btn:hover { transform: translateY(-2px); box-shadow: 0 4px 12px rgba(0,0,0,0.15); }
        .controls { margin-bottom: 20px; text-align: center; }
        .connection-status {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 15px;
            border-radius: 20px;
            color: white;
            font-size: 12px;
            z-index: 1000;
        }
        .connected { background: #28a745; }
        .disconnected { background: #dc3545; }
        .three-column { grid-template-columns: 1fr 1fr 1fr; }
        .performance-summary {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
        }
        .perf-item {
            text-align: center;
            flex: 1;
        }
        .perf-value { font-size: 1.5em; font-weight: bold; color: #2a5298; }
        .perf-label { font-size: 0.9em; color: #666; }
        .video-player {
            width: 100%;
            height: 300px;
            background: #000;
            border-radius: 8px;
            object-fit: contain;
        }
        .player-controls {
            margin-top: 15px;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            justify-content: center;
        }
        .protocol-selector {
            display: flex;
            gap: 5px;
            margin-bottom: 10px;
        }
        .protocol-btn {
            padding: 5px 15px;
            border: 2px solid #2a5298;
            background: white;
            color: #2a5298;
            border-radius: 20px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s;
        }
        .protocol-btn.active {
            background: #2a5298;
            color: white;
        }
        .protocol-btn:hover {
            background: #1e3c72;
            color: white;
            border-color: #1e3c72;
        }
        .player-info {
            margin-top: 10px;
            font-size: 12px;
            color: #666;
            display: flex;
            justify-content: space-between;
        }
        .stream-status {
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 11px;
            font-weight: bold;
        }
        .stream-online { background: #d4edda; color: #155724; }
        .stream-offline { background: #f8d7da; color: #721c24; }
        .player-stats {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
            font-size: 11px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="connection-status" id="connectionStatus">🔴 未连接</div>
    
    <div class="header">
        <h1>📊 FFmpeg实时监控面板</h1>
        <p>实时监控推流状态 | 性能分析 | 日志跟踪</p>
    </div>

    <div class="container">
        <!-- 状态概览 -->
        <div class="panel full-width">
            <h3>📈 实时状态概览</h3>
            <div class="status-grid">
                <div class="status-card">
                    <h4>推流状态</h4>
                    <div class="value" id="streamStatus">停止</div>
                </div>
                <div class="status-card">
                    <h4>运行时间</h4>
                    <div class="value" id="uptime">0</div>
                    <div class="unit">秒</div>
                </div>
                <div class="status-card">
                    <h4>处理帧数</h4>
                    <div class="value" id="frameCount">0</div>
                    <div class="unit">帧</div>
                </div>
                <div class="status-card">
                    <h4>CPU使用率</h4>
                    <div class="value" id="cpuUsage">0</div>
                    <div class="unit">%</div>
                </div>
                <div class="status-card">
                    <h4>内存使用</h4>
                    <div class="value" id="memoryUsage">0</div>
                    <div class="unit">MB</div>
                </div>
                <div class="status-card">
                    <h4>错误计数</h4>
                    <div class="value" id="errorCount">0</div>
                    <div class="unit">个</div>
                </div>
            </div>
            <div class="controls">
                <button class="btn btn-primary" onclick="refreshData()">🔄 刷新数据</button>
                <button class="btn btn-success" onclick="startMonitor()">▶️ 开始监控</button>
                <button class="btn btn-warning" onclick="stopMonitor()">⏸️ 停止监控</button>
                <button class="btn btn-danger" onclick="clearData()">🗑️ 清空数据</button>
                <a href="/" class="btn btn-primary">🏠 返回主页</a>
            </div>
        </div>

        <!-- 视频播放器 -->
        <div class="panel">
            <h3>📺 实时视频监控</h3>
            <div class="protocol-selector">
                <button class="protocol-btn active" data-protocol="hls">HLS</button>
                <button class="protocol-btn" data-protocol="flv">FLV</button>
                <button class="protocol-btn" data-protocol="webrtc">WebRTC</button>
            </div>
            <video id="videoPlayer" class="video-player" controls muted>
                您的浏览器不支持视频播放
            </video>
            <div class="player-info">
                <span>协议: <span id="currentProtocol">HLS</span></span>
                <span class="stream-status stream-offline" id="streamStatus">离线</span>
            </div>
            <div class="player-controls">
                <button class="btn btn-success" onclick="startPlay()">▶️ 开始播放</button>
                <button class="btn btn-warning" onclick="stopPlay()">⏸️ 停止播放</button>
                <button class="btn btn-primary" onclick="refreshPlayer()">🔄 刷新</button>
                <button class="btn btn-info" onclick="toggleMute()">🔊 静音</button>
            </div>
            <div class="player-stats" id="playerStats">
                等待播放器数据...
            </div>
            <div class="player-debug" id="playerDebug" style="margin-top: 10px; padding: 10px; background: #fff3cd; border-radius: 5px; font-size: 11px; display: none;">
                <strong>调试信息:</strong><br>
                <span id="debugInfo">调试模式已开启</span>
            </div>
            <div style="text-align: center; margin-top: 10px;">
                <button class="btn btn-warning" onclick="toggleDebug()" style="font-size: 11px; padding: 5px 10px;">🐛 调试</button>
            </div>
        </div>

        <!-- 性能图表 -->
        <div class="panel">
            <h3>⚡ CPU & 内存使用率</h3>
            <div class="chart-container">
                <canvas id="performanceChart"></canvas>
            </div>
        </div>

        <!-- 比特率图表 -->
        <div class="panel">
            <h3>📊 比特率监控</h3>
            <div class="chart-container">
                <canvas id="bitrateChart"></canvas>
            </div>
        </div>

        <!-- 实时日志 -->
        <div class="panel full-width">
            <h3>📝 FFmpeg实时日志</h3>
            <div class="performance-summary">
                <div class="perf-item">
                    <div class="perf-value" id="totalLogs">0</div>
                    <div class="perf-label">总日志数</div>
                </div>
                <div class="perf-item">
                    <div class="perf-value" id="warningCount">0</div>
                    <div class="perf-label">警告数</div>
                </div>
                <div class="perf-item">
                    <div class="perf-value" id="errorLogCount">0</div>
                    <div class="perf-label">错误数</div>
                </div>
            </div>
            <div class="log-container" id="logContainer">
                <div class="log-entry log-info">
                    <span class="log-timestamp">[等待数据]</span> 监控系统已就绪，等待FFmpeg进程启动...
                </div>
            </div>
        </div>
    </div>

    <script>
        // WebSocket连接
        const socket = io();
        let performanceChart, bitrateChart;
        
        // 播放器相关变量
        let currentPlayer = null;
        let currentProtocol = 'hls';
        let playerStats = {
            protocol: '',
            url: '',
            resolution: '',
            fps: 0,
            bitrate: 0,
            bufferHealth: 0,
            errors: 0
        };
        
        // 连接状态管理
        socket.on('connect', function() {
            document.getElementById('connectionStatus').innerHTML = '🟢 已连接';
            document.getElementById('connectionStatus').className = 'connection-status connected';
        });
        
        socket.on('disconnect', function() {
            document.getElementById('connectionStatus').innerHTML = '🔴 未连接';
            document.getElementById('connectionStatus').className = 'connection-status disconnected';
        });
        
        // 实时数据更新
        socket.on('monitor_update', function(data) {
            updateStatusCards(data);
            updateCharts(data);
        });
        
        // 初始化图表
        function initCharts() {
            // 性能图表
            const perfCtx = document.getElementById('performanceChart').getContext('2d');
            performanceChart = new Chart(perfCtx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: 'CPU使用率 (%)',
                        data: [],
                        borderColor: '#ff6384',
                        backgroundColor: 'rgba(255, 99, 132, 0.1)',
                        tension: 0.4
                    }, {
                        label: '内存使用 (MB)',
                        data: [],
                        borderColor: '#36a2eb',
                        backgroundColor: 'rgba(54, 162, 235, 0.1)',
                        tension: 0.4,
                        yAxisID: 'y1'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            title: { display: true, text: 'CPU (%)' }
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            title: { display: true, text: '内存 (MB)' },
                            grid: { drawOnChartArea: false }
                        }
                    },
                    plugins: {
                        legend: { display: true },
                        title: { display: true, text: '实时性能监控' }
                    }
                }
            });
            
            // 比特率图表
            const bitrateCtx = document.getElementById('bitrateChart').getContext('2d');
            bitrateChart = new Chart(bitrateCtx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: '比特率 (kbps)',
                        data: [],
                        borderColor: '#4bc0c0',
                        backgroundColor: 'rgba(75, 192, 192, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            title: { display: true, text: '比特率 (kbps)' }
                        }
                    },
                    plugins: {
                        legend: { display: true },
                        title: { display: true, text: '推流比特率监控' }
                    }
                }
            });
        }
        
        // 更新状态卡片
        function updateStatusCards(data) {
            if (data.cpu_percent !== undefined) {
                document.getElementById('cpuUsage').textContent = data.cpu_percent.toFixed(1);
            }
            if (data.memory_mb !== undefined) {
                document.getElementById('memoryUsage').textContent = Math.round(data.memory_mb);
            }
            if (data.frame_count !== undefined) {
                document.getElementById('frameCount').textContent = data.frame_count;
            }
            if (data.uptime !== undefined) {
                document.getElementById('uptime').textContent = Math.round(data.uptime);
            }
        }
        
        // 更新图表
        function updateCharts(data) {
            const now = new Date().toLocaleTimeString();
            
            // 更新性能图表
            if (performanceChart && data.cpu_percent !== undefined) {
                performanceChart.data.labels.push(now);
                performanceChart.data.datasets[0].data.push(data.cpu_percent);
                performanceChart.data.datasets[1].data.push(data.memory_mb);
                
                // 保持最近20个数据点
                if (performanceChart.data.labels.length > 20) {
                    performanceChart.data.labels.shift();
                    performanceChart.data.datasets[0].data.shift();
                    performanceChart.data.datasets[1].data.shift();
                }
                performanceChart.update('none');
            }
        }
        
        // 刷新数据
        function refreshData() {
            fetch('/api/monitor/data')
                .then(response => response.json())
                .then(data => {
                    updateStatusDisplay(data);
                    updateLogDisplay(data.logs || []);
                    updateBitrateChart(data.bitrates || []);
                })
                .catch(error => console.error('获取数据失败:', error));
        }
        
        // 更新状态显示
        function updateStatusDisplay(data) {
            const status = data.status || {};
            const summary = data.summary || {};
            
            document.getElementById('streamStatus').textContent = status.is_running ? '运行中' : '停止';
            document.getElementById('frameCount').textContent = summary.current_frame || 0;
            document.getElementById('uptime').textContent = Math.round(summary.uptime || 0);
            document.getElementById('errorCount').textContent = summary.error_count || 0;
            document.getElementById('totalLogs').textContent = summary.total_logs || 0;
            document.getElementById('warningCount').textContent = summary.warning_count || 0;
            document.getElementById('errorLogCount').textContent = summary.error_count || 0;
        }
        
        // 更新日志显示
        function updateLogDisplay(logs) {
            const container = document.getElementById('logContainer');
            container.innerHTML = '';
            
            logs.forEach(log => {
                const div = document.createElement('div');
                div.className = `log-entry log-${log.type || 'info'}`;
                div.innerHTML = `
                    <span class="log-timestamp">[${new Date(log.timestamp).toLocaleTimeString()}]</span>
                    ${log.content}
                `;
                container.appendChild(div);
            });
            
            container.scrollTop = container.scrollHeight;
        }
        
        // 更新比特率图表
        function updateBitrateChart(bitrates) {
            if (!bitrateChart) return;
            
            bitrateChart.data.labels = bitrates.map(b => new Date(b.timestamp).toLocaleTimeString());
            bitrateChart.data.datasets[0].data = bitrates.map(b => b.bitrate);
            bitrateChart.update();
        }
        
        // 控制函数
        function startMonitor() {
            fetch('/api/monitor/start', { method: 'POST' })
                .then(response => response.json())
                .then(data => alert(data.msg));
        }
        
        function stopMonitor() {
            fetch('/api/monitor/stop', { method: 'POST' })
                .then(response => response.json())
                .then(data => alert(data.msg));
        }
        
        function clearData() {
            if (confirm('确定要清空所有监控数据吗？')) {
                fetch('/api/monitor/clear', { method: 'POST' })
                    .then(response => response.json())
                    .then(data => {
                        alert(data.msg);
                        refreshData();
                    });
            }
        }
        
        // 播放器功能
        function initPlayer() {
            // 协议选择器事件
            document.querySelectorAll('.protocol-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    document.querySelectorAll('.protocol-btn').forEach(b => b.classList.remove('active'));
                    this.classList.add('active');
                    currentProtocol = this.dataset.protocol;
                    document.getElementById('currentProtocol').textContent = currentProtocol.toUpperCase();
                    stopPlay(); // 切换协议时停止当前播放
                });
            });
        }
        
        async function startPlay() {
            const video = document.getElementById('videoPlayer');
            const streamId = 'stream1'; // 默认流ID
            
            await stopPlay(); // 先停止当前播放
            
            try {
                switch(currentProtocol) {
                    case 'hls':
                        await startHLSPlay(video, streamId);
                        break;
                    case 'flv':
                        await startFLVPlay(video, streamId);
                        break;
                    case 'webrtc':
                        await startWebRTCPlay(video, streamId);
                        break;
                }
                
                updateStreamStatus('online');
                updatePlayerStats();
            } catch (error) {
                console.error('播放启动失败:', error);
                updateStreamStatus('offline');
                alert(`播放失败: ${error.message}`);
            }
        }
        
        async function startHLSPlay(video, streamId) {
            const hlsUrl = `http://127.0.0.1:8080/live/${streamId}/hls.m3u8`;
            playerStats.url = hlsUrl;
            playerStats.protocol = 'HLS';
            
            if (Hls.isSupported()) {
                currentPlayer = new Hls({
                    debug: false,
                    enableWorker: true,
                    lowLatencyMode: true
                });
                
                currentPlayer.loadSource(hlsUrl);
                currentPlayer.attachMedia(video);
                
                currentPlayer.on(Hls.Events.MANIFEST_PARSED, function() {
                    video.play();
                });
                
                currentPlayer.on(Hls.Events.ERROR, function(event, data) {
                    console.error('HLS错误:', data);
                    playerStats.errors++;
                    if (data.fatal) {
                        updateStreamStatus('offline');
                    }
                });
                
                currentPlayer.on(Hls.Events.LEVEL_LOADED, function(event, data) {
                    playerStats.bitrate = data.details.totalduration > 0 ? 
                        (data.details.totalBytes * 8 / data.details.totalduration / 1000) : 0;
                });
                
            } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
                // Safari原生支持
                video.src = hlsUrl;
                video.play();
            } else {
                throw new Error('浏览器不支持HLS播放');
            }
        }
        
        async function startFLVPlay(video, streamId) {
            const flvUrl = `http://127.0.0.1:8080/live/${streamId}.live.flv`;
            playerStats.url = flvUrl;
            playerStats.protocol = 'FLV';
            
            if (flvjs.isSupported()) {
                currentPlayer = flvjs.createPlayer({
                    type: 'flv',
                    url: flvUrl,
                    isLive: true,
                    hasAudio: true,
                    hasVideo: true
                }, {
                    enableWorker: false,
                    enableStashBuffer: false,
                    stashInitialSize: 128,
                    lazyLoad: false,
                    autoCleanupSourceBuffer: true
                });
                
                currentPlayer.attachMediaElement(video);
                currentPlayer.load();
                
                currentPlayer.on(flvjs.Events.LOADING_COMPLETE, function() {
                    video.play();
                });
                
                currentPlayer.on(flvjs.Events.ERROR, function(errorType, errorDetail) {
                    console.error('FLV错误:', errorType, errorDetail);
                    playerStats.errors++;
                    updateStreamStatus('offline');
                });
                
                currentPlayer.on(flvjs.Events.STATISTICS_INFO, function(res) {
                    playerStats.fps = res.fps || 0;
                    playerStats.bitrate = res.speed || 0;
                });
                
            } else {
                throw new Error('浏览器不支持FLV播放');
            }
        }
        
        async function startWebRTCPlay(video, streamId) {
            playerStats.protocol = 'WebRTC';
            playerStats.url = `webrtc://127.0.0.1:8080/live/${streamId}`;
            
            try {
                // 检查是否支持WebRTC
                if (!window.RTCPeerConnection) {
                    throw new Error('浏览器不支持WebRTC');
                }
                
                // 创建WebRTC播放器实例
                if (window.ZLMWebRTCPlayer) {
                    currentPlayer = new ZLMWebRTCPlayer(video, {
                        stream: streamId,
                        app: 'live',
                        vhost: '__defaultVhost__'
                    });
                    
                    // 设置错误回调
                    currentPlayer.setErrorCallback((error) => {
                        console.error('WebRTC播放错误:', error);
                        updateStreamStatus('offline');
                        playerStats.errors++;
                    });
                    
                    // 开始播放
                    await currentPlayer.play();
                    console.log('WebRTC播放启动成功');
                    
                    // 定期更新统计信息
                    if (window.webrtcStatsInterval) {
                        clearInterval(window.webrtcStatsInterval);
                    }
                    
                    window.webrtcStatsInterval = setInterval(async () => {
                        if (currentPlayer && currentPlayer.isPlaying) {
                            const stats = await currentPlayer.getStats();
                            playerStats.fps = stats.fps;
                            playerStats.bitrate = stats.bitrate;
                            playerStats.resolution = stats.resolution;
                            updatePlayerStatsDisplay();
                        }
                    }, 2000);
                    
                } else {
                    throw new Error('WebRTC播放器未加载');
                }
            } catch (error) {
                console.error('WebRTC播放失败:', error);
                
                // 降级到HLS播放
                console.log('降级到HLS播放');
                alert(`WebRTC播放失败: ${error.message}\n自动切换到HLS播放`);
                
                // 切换到HLS
                currentProtocol = 'hls';
                document.querySelector('[data-protocol="hls"]').classList.add('active');
                document.querySelector('[data-protocol="webrtc"]').classList.remove('active');
                document.getElementById('currentProtocol').textContent = 'HLS (WebRTC降级)';
                
                await startHLSPlay(video, streamId);
            }
        }
        
        async function stopPlay() {
            const video = document.getElementById('videoPlayer');
            
            // 清理WebRTC统计定时器
            if (window.webrtcStatsInterval) {
                clearInterval(window.webrtcStatsInterval);
                window.webrtcStatsInterval = null;
            }
            
            if (currentPlayer) {
                try {
                    if (currentPlayer.stop) {
                        // WebRTC播放器
                        await currentPlayer.stop();
                    } else if (currentPlayer.destroy) {
                        // HLS播放器
                        currentPlayer.destroy();
                    } else if (currentPlayer.unload) {
                        // FLV播放器
                        currentPlayer.unload();
                    }
                } catch (e) {
                    console.warn('播放器清理失败:', e);
                }
                currentPlayer = null;
            }
            
            // 清空视频源
            video.src = '';
            video.srcObject = null;
            video.load();
            
            updateStreamStatus('offline');
            playerStats = {
                protocol: currentProtocol.toUpperCase(),
                url: '',
                resolution: '',
                fps: 0,
                bitrate: 0,
                bufferHealth: 0,
                errors: 0
            };
            updatePlayerStatsDisplay();
        }
        
        async function refreshPlayer() {
            if (currentPlayer) {
                await stopPlay();
                setTimeout(async () => await startPlay(), 1000);
            }
        }
        
        function toggleMute() {
            const video = document.getElementById('videoPlayer');
            video.muted = !video.muted;
            document.querySelector('[onclick="toggleMute()"]').textContent = 
                video.muted ? '🔇 取消静音' : '🔊 静音';
        }
        
        function updateStreamStatus(status) {
            const statusEl = document.getElementById('streamStatus');
            statusEl.textContent = status === 'online' ? '在线' : '离线';
            statusEl.className = `stream-status stream-${status}`;
        }
        
        function updatePlayerStats() {
            if (!currentPlayer) return;
            
            const video = document.getElementById('videoPlayer');
            
            // 更新分辨率
            if (video.videoWidth && video.videoHeight) {
                playerStats.resolution = `${video.videoWidth}x${video.videoHeight}`;
            }
            
            // 更新缓冲健康度
            if (video.buffered.length > 0) {
                const buffered = video.buffered.end(video.buffered.length - 1);
                const currentTime = video.currentTime;
                playerStats.bufferHealth = Math.max(0, buffered - currentTime);
            }
            
            updatePlayerStatsDisplay();
        }
        
        function updatePlayerStatsDisplay() {
            const statsEl = document.getElementById('playerStats');
            statsEl.innerHTML = `
                协议: ${playerStats.protocol}<br>
                URL: ${playerStats.url}<br>
                分辨率: ${playerStats.resolution || '未知'}<br>
                帧率: ${playerStats.fps.toFixed(1)} FPS<br>
                比特率: ${playerStats.bitrate.toFixed(0)} kbps<br>
                缓冲: ${playerStats.bufferHealth.toFixed(1)}s<br>
                错误: ${playerStats.errors}
            `;
        }

        // 调试功能
        function toggleDebug() {
            const debugDiv = document.getElementById('playerDebug');
            debugDiv.style.display = debugDiv.style.display === 'none' ? 'block' : 'none';
            
            if (debugDiv.style.display === 'block') {
                updateDebugInfo();
            }
        }
        
        function updateDebugInfo() {
            const debugInfo = document.getElementById('debugInfo');
            const info = [];
            
            info.push(`WebRTC播放器类: ${!!window.ZLMWebRTCPlayer ? '✅' : '❌'}`);
            info.push(`HLS.js: ${!!window.Hls ? '✅' : '❌'}`);
            info.push(`FLV.js: ${!!window.flvjs ? '✅' : '❌'}`);
            info.push(`WebRTC支持: ${!!window.RTCPeerConnection ? '✅' : '❌'}`);
            info.push(`当前协议: ${currentProtocol}`);
            info.push(`播放器实例: ${currentPlayer ? '有' : '无'}`);
            info.push(`浏览器: ${navigator.userAgent.split(' ')[0]}`);
            
            debugInfo.innerHTML = info.join('<br>');
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initCharts();
            initPlayer();
            refreshData();
            setInterval(refreshData, 5000); // 每5秒刷新一次
            setInterval(updatePlayerStats, 2000); // 每2秒更新播放器统计
            
            // 输出调试信息到控制台
            console.log('=== FFmpeg监控面板调试信息 ===');
            console.log('WebRTC播放器类:', !!window.ZLMWebRTCPlayer);
            console.log('HLS.js:', !!window.Hls);
            console.log('FLV.js:', !!window.flvjs);
            console.log('WebRTC支持:', !!window.RTCPeerConnection);
            console.log('页面URL:', window.location.href);
        });
    </script>
</body>
</html>
'''

if __name__ == '__main__':
    print("本地FFmpeg推流控制器已启动")
    print("访问地址: http://127.0.0.1:5000")
    # 修复Windows下debug模式的模块导入问题
    # 使用SocketIO运行，支持WebSocket实时通信
    socketio.run(app, host='0.0.0.0', port=5000, debug=False)