#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试文件路径修复功能
"""

import os
import sys

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(__file__))
from stream_controller import LocalFFmpegController

def test_path_fix():
    controller = LocalFFmpegController()
    
    # 测试各种可能的错误路径格式
    test_paths = [
        r"D:\Dev\ZLMediaKit\ZLMediaKit\Video-files\test_video1.mp4",  # 正常路径
        "D:DevZLMediaKitZLMediaKitVideo-files\test_video1.mp4",       # 缺少分隔符
        "D:Dev\\ZLMediaKit\\ZLMediaKit\\Video-files\\test_video1.mp4", # 混合分隔符
        "D:/Dev/ZLMediaKit/ZLMediaKit/Video-files/test_video1.mp4",   # Unix风格
    ]
    
    print("=== 路径修复测试 ===")
    for i, path in enumerate(test_paths, 1):
        print(f"\n测试 {i}: {repr(path)}")
        fixed_path = controller.fix_file_path(path)
        print(f"修复后: {repr(fixed_path)}")
        print(f"文件存在: {os.path.exists(fixed_path) if fixed_path else False}")
    
    # 测试视频文件扫描
    print("\n=== 视频文件扫描测试 ===")
    files = controller.get_video_files()
    for file_info in files:
        print(f"文件: {file_info['name']}")
        print(f"路径: {repr(file_info['path'])}")
        print(f"存在: {os.path.exists(file_info['path'])}")
        print(f"大小: {file_info['size']} bytes")
        print("---")

if __name__ == '__main__':
    test_path_fix() 