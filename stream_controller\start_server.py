#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FFmpeg推流控制器启动脚本
解决Windows下Flask debug模式的模块导入问题
"""

import sys
import os
import argparse

# 添加当前目录到Python路径，解决模块导入问题
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

# 导入控制器
from stream_controller import app, controller, socketio

def main():
    parser = argparse.ArgumentParser(description='FFmpeg推流控制器')
    parser.add_argument('--debug', action='store_true', help='启用调试模式')
    parser.add_argument('--port', type=int, default=5000, help='端口号 (默认: 5000)')
    parser.add_argument('--host', default='0.0.0.0', help='主机地址 (默认: 0.0.0.0)')
    
    args = parser.parse_args()
    
    print("=" * 60)
    print("🎬 本地FFmpeg推流控制器")
    print("=" * 60)
    print(f"📡 服务地址: http://127.0.0.1:{args.port}")
    print(f"🌐 网络地址: http://{args.host}:{args.port}")
    print(f"📁 视频目录: {controller.video_dir}")
    print(f"📺 推流地址: {controller.rtmp_url}")
    print("=" * 60)
    
    try:
        if args.debug:
            print("⚠️  调试模式已启用（关闭自动重载以避免模块导入问题）")
            socketio.run(
                app,
                host=args.host, 
                port=args.port, 
                debug=True, 
                use_reloader=False  # 关闭自动重载
            )
        else:
            print("✅ 生产模式启动")
            socketio.run(
                app,
                host=args.host, 
                port=args.port, 
                debug=False
            )
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main() 