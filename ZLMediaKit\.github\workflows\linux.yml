name: Linux

on: [push, pull_request]

jobs:
  build:

    runs-on: ubuntu-24.04

    steps:
    - uses: actions/checkout@v1

    - name: 下载submodule源码
      run: mv -f .gitmodules_github .gitmodules && git submodule sync && git submodule update --init

    - name: 下载 SRTP
      uses: actions/checkout@v2
      with:
        repository: cisco/libsrtp
        fetch-depth: 1
        ref: v2.3.0
        path: 3rdpart/libsrtp

    - name: 下载 openssl
      uses: actions/checkout@v2
      with:
        repository: openssl/openssl
        fetch-depth: 1
        ref: OpenSSL_1_1_1
        path: 3rdpart/openssl

    - name: 下载 usrsctp
      uses: actions/checkout@v2
      with:
        repository: sctplab/usrsctp
        fetch-depth: 1
        ref: 0.9.5.0
        path: 3rdpart/usrsctp

    - name: 启动 Docker 容器, 在Docker 容器中执行脚本
      run: |
        docker pull centos:7 
        docker run -v $(pwd):/root -w /root --rm centos:7 sh -c "
          #!/bin/bash
          set -x
      
          # Backup original CentOS-Base.repo file
          cp /etc/yum.repos.d/CentOS-Base.repo /etc/yum.repos.d/CentOS-Base.repo.backup
      
          # Define new repository configuration
          cat <<EOF > /etc/yum.repos.d/CentOS-Base.repo
        [base]
        name=CentOS-7 - Base - mirrors.aliyun.com
        baseurl=http://mirrors.aliyun.com/centos/7/os/x86_64/
        gpgcheck=1
        gpgkey=http://mirrors.aliyun.com/centos/RPM-GPG-KEY-CentOS-7
      
        [updates]
        name=CentOS-7 - Updates - mirrors.aliyun.com
        baseurl=http://mirrors.aliyun.com/centos/7/updates/x86_64/
        gpgcheck=1
        gpgkey=http://mirrors.aliyun.com/centos/RPM-GPG-KEY-CentOS-7
        EOF
      
          # Clean yum cache and recreate it
          yum clean all
          yum makecache
      
          echo \"CentOS 7 软件源已成功切换\"
          yum install -y git wget gcc gcc-c++ make
        
          mkdir -p /root/install
          
          cd 3rdpart/openssl
          ./config no-shared --prefix=/root/install
          make -j $(nproc)
          make install
          cd ../../
        
          wget https://github.com/Kitware/CMake/releases/download/v3.29.5/cmake-3.29.5.tar.gz
          tar -xf cmake-3.29.5.tar.gz
          cd cmake-3.29.5
          OPENSSL_ROOT_DIR=/root/install ./configure
          make -j $(nproc)
          make install
          cd ..
        
          cd 3rdpart/usrsctp
          mkdir build
          cd build
          cmake -DCMAKE_BUILD_TYPE=Release -DCMAKE_POSITION_INDEPENDENT_CODE=ON ..
          make -j $(nproc)
          make install
          cd ../../../
        
          cd 3rdpart/libsrtp && ./configure --enable-openssl  --with-openssl-dir=/root/install && make -j $(nproc) && make install
          cd ../../
        
          mkdir -p linux_build && cd linux_build && cmake .. -DOPENSSL_ROOT_DIR=/root/install -DCMAKE_BUILD_TYPE=Release && make -j $(nproc)
        "

    - name: 设置环境变量
      run: |
        echo "BRANCH=$(echo ${GITHUB_REF#refs/heads/} | tr -s "/\?%*:|\"<>" "_")" >> $GITHUB_ENV  
        echo "BRANCH2=$(echo ${GITHUB_REF#refs/heads/} )" >> $GITHUB_ENV    
        echo "DATE=$(date +%Y-%m-%d)" >> $GITHUB_ENV

    - name: 打包二进制
      id: upload
      uses: actions/upload-artifact@v4
      with:
        name: ${{ github.workflow }}_${{ env.BRANCH }}_${{ env.DATE }}
        path: release/*
        if-no-files-found: error
        retention-days: 90

    - name: issue评论
      if: github.event_name != 'pull_request' && github.ref != 'refs/heads/feature/test'
      uses: actions/github-script@v7
      with:
        github-token: ${{ secrets.GITHUB_TOKEN }}
        script: |
          github.rest.issues.createComment({
            issue_number: ${{vars.VERSION_ISSUE_NO}},
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: '- 下载地址: [${{ github.workflow }}_${{ env.BRANCH }}_${{ env.DATE }}](${{ steps.upload.outputs.artifact-url }})\n'
              + '- 分支: ${{ env.BRANCH2 }}\n' 
              + '- git hash: ${{ github.sha }} \n' 
              + '- 编译日期: ${{ env.DATE }}\n' 
              + '- 编译记录: [${{ github.run_id }}](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }})\n'
              + '- 打包ci名: ${{ github.workflow }}\n'
              + '- 开启特性: openssl/webrtc/datachannel\n'
              + '- 说明: 本二进制在centos7(x64)上编译，请确保您的机器系统不低于此版本\n' 
          })
