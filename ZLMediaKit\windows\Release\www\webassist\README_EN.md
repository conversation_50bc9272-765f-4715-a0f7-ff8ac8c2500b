# zlm_webassist


[![](https://img.shields.io/badge/license-MIT-green.svg)](https://github.com/1002victor/zlm_webassist/blob/main/LICENSE)
![](https://img.shields.io/badge/language-html-red.svg)
![](https://img.shields.io/badge/language-vue-green.svg)
![](https://img.shields.io/badge/language-js-black.svg)
![](https://img.shields.io/badge/language-css-yelllow.svg)
[![](https://img.shields.io/badge/platform-linux%20|%20macos%20|%20windows-blue.svg)](https://github.com/ZLMediaKit/ZLMediaKit)
[![](https://img.shields.io/badge/PRs-welcome-yellow.svg)](https://github.com/1002victor/zlm_webassist/pulls)

[简体中文](./README.md) | English

ZLMediakit's web management assistant
