#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
监控功能相关的单元测试
采用TDD方式：先写测试，再实现功能
"""

import pytest
import time
import threading
from unittest.mock import patch, Mock, MagicMock
from collections import deque
from stream_controller import LocalFFmpegController


class TestPerformanceMonitoring:
    """性能监控测试"""
    
    def test_start_monitoring_success(self, controller_instance, mock_psutil):
        """测试成功启动监控"""
        # Arrange
        controller_instance.current_process = Mock()
        controller_instance.current_process.pid = 12345
        
        # Act
        if hasattr(controller_instance, 'start_monitoring'):
            result = controller_instance.start_monitoring()
            
            # Assert
            assert controller_instance.is_monitoring is True
            assert controller_instance.monitor_thread is not None
            assert controller_instance.monitor_thread.is_alive()
        else:
            # 如果方法不存在，我们需要测试它应该存在
            assert hasattr(controller_instance, 'is_monitoring')
            assert hasattr(controller_instance, 'monitor_thread')
    
    def test_start_monitoring_no_process(self, controller_instance):
        """测试没有进程时启动监控"""
        # Arrange
        controller_instance.current_process = None
        
        # Act
        if hasattr(controller_instance, 'start_monitoring'):
            result = controller_instance.start_monitoring()
            
            # Assert
            # 应该返回错误或不启动监控
            if isinstance(result, dict):
                assert result['code'] != 0
            else:
                assert controller_instance.is_monitoring is False
        else:
            pytest.skip("start_monitoring method not implemented yet")
    
    def test_stop_monitoring_success(self, controller_instance):
        """测试成功停止监控"""
        # Arrange
        controller_instance.is_monitoring = True
        controller_instance.monitor_thread = Mock()
        controller_instance.monitor_thread.is_alive.return_value = True
        
        # Act
        if hasattr(controller_instance, 'stop_monitoring'):
            result = controller_instance.stop_monitoring()
            
            # Assert
            assert controller_instance.is_monitoring is False
        else:
            pytest.skip("stop_monitoring method not implemented yet")
    
    def test_collect_performance_data(self, controller_instance, mock_psutil):
        """测试收集性能数据"""
        # Arrange
        controller_instance.current_process = Mock()
        controller_instance.current_process.pid = 12345
        
        # Act
        if hasattr(controller_instance, 'collect_performance_data'):
            data = controller_instance.collect_performance_data()
            
            # Assert
            assert isinstance(data, dict)
            assert 'cpu_percent' in data
            assert 'memory_mb' in data
            assert 'timestamp' in data
            assert isinstance(data['cpu_percent'], (int, float))
            assert isinstance(data['memory_mb'], (int, float))
            assert data['cpu_percent'] >= 0
            assert data['memory_mb'] >= 0
        else:
            pytest.skip("collect_performance_data method not implemented yet")
    
    def test_performance_data_storage(self, controller_instance, mock_psutil):
        """测试性能数据存储"""
        # Arrange
        controller_instance.current_process = Mock()
        controller_instance.current_process.pid = 12345
        
        # Act
        if hasattr(controller_instance, 'collect_performance_data'):
            # 收集多次数据
            for i in range(5):
                data = controller_instance.collect_performance_data()
                if hasattr(controller_instance, 'store_performance_data'):
                    controller_instance.store_performance_data(data)
            
            # Assert
            assert len(controller_instance.performance_data) <= 100  # 最大存储100个数据点
            assert isinstance(controller_instance.performance_data, deque)
        else:
            pytest.skip("collect_performance_data method not implemented yet")
    
    def test_performance_data_limits(self, controller_instance):
        """测试性能数据存储限制"""
        # Arrange
        controller_instance.performance_data = deque(maxlen=100)
        
        # Act - 添加超过限制的数据
        for i in range(150):
            data = {
                'cpu_percent': i,
                'memory_mb': i * 10,
                'timestamp': time.time()
            }
            controller_instance.performance_data.append(data)
        
        # Assert
        assert len(controller_instance.performance_data) == 100
        # 最早的数据应该被移除
        assert controller_instance.performance_data[0]['cpu_percent'] == 50


class TestLogMonitoring:
    """日志监控测试"""
    
    def test_ffmpeg_log_collection(self, controller_instance, mock_subprocess):
        """测试FFmpeg日志收集"""
        # Arrange
        mock_popen, mock_process = mock_subprocess
        mock_process.stderr.readline.side_effect = [
            b"frame=  100 fps= 30 q=28.0 size=    1024kB time=00:00:03.33 bitrate=2516.1kbits/s speed=   1x",
            b"frame=  200 fps= 30 q=28.0 size=    2048kB time=00:00:06.67 bitrate=2516.1kbits/s speed=   1x",
            b""  # EOF
        ]
        
        # Act
        if hasattr(controller_instance, 'collect_ffmpeg_logs'):
            controller_instance.current_process = mock_process
            logs = controller_instance.collect_ffmpeg_logs()
            
            # Assert
            assert isinstance(logs, list)
            assert len(logs) >= 2
            assert 'frame=' in logs[0]
            assert 'bitrate=' in logs[0]
        else:
            pytest.skip("collect_ffmpeg_logs method not implemented yet")
    
    def test_log_parsing(self, controller_instance):
        """测试日志解析"""
        # Arrange
        log_line = "frame=  100 fps= 30 q=28.0 size=    1024kB time=00:00:03.33 bitrate=2516.1kbits/s speed=   1x"
        
        # Act
        if hasattr(controller_instance, 'parse_ffmpeg_log'):
            parsed = controller_instance.parse_ffmpeg_log(log_line)
            
            # Assert
            assert isinstance(parsed, dict)
            assert 'frame' in parsed
            assert 'fps' in parsed
            assert 'bitrate' in parsed
            assert parsed['frame'] == 100
            assert parsed['fps'] == 30
            assert '2516.1' in str(parsed['bitrate'])
        else:
            pytest.skip("parse_ffmpeg_log method not implemented yet")
    
    def test_log_storage_limits(self, controller_instance):
        """测试日志存储限制"""
        # Arrange
        controller_instance.ffmpeg_logs = deque(maxlen=1000)
        
        # Act - 添加超过限制的日志
        for i in range(1500):
            log_entry = f"frame={i} fps=30 bitrate=2000kbits/s"
            controller_instance.ffmpeg_logs.append(log_entry)
        
        # Assert
        assert len(controller_instance.ffmpeg_logs) == 1000
        # 最早的日志应该被移除
        assert "frame=500" in controller_instance.ffmpeg_logs[0]
    
    def test_log_filtering(self, controller_instance):
        """测试日志过滤"""
        # Arrange
        logs = [
            "frame=  100 fps= 30 q=28.0 size=    1024kB time=00:00:03.33 bitrate=2516.1kbits/s speed=   1x",
            "[error] Invalid data found when processing input",
            "frame=  200 fps= 30 q=28.0 size=    2048kB time=00:00:06.67 bitrate=2516.1kbits/s speed=   1x",
            "[warning] Deprecated pixel format used",
            "frame=  300 fps= 30 q=28.0 size=    3072kB time=00:00:10.00 bitrate=2516.1kbits/s speed=   1x"
        ]
        
        # Act
        if hasattr(controller_instance, 'filter_logs'):
            error_logs = controller_instance.filter_logs(logs, level='error')
            warning_logs = controller_instance.filter_logs(logs, level='warning')
            frame_logs = controller_instance.filter_logs(logs, pattern='frame=')
            
            # Assert
            assert len(error_logs) == 1
            assert len(warning_logs) == 1
            assert len(frame_logs) == 3
            assert '[error]' in error_logs[0]
            assert '[warning]' in warning_logs[0]
        else:
            pytest.skip("filter_logs method not implemented yet")


class TestBitrateMonitoring:
    """比特率监控测试"""
    
    def test_bitrate_extraction(self, controller_instance):
        """测试比特率提取"""
        # Arrange
        log_line = "frame=  100 fps= 30 q=28.0 size=    1024kB time=00:00:03.33 bitrate=2516.1kbits/s speed=   1x"
        
        # Act
        if hasattr(controller_instance, 'extract_bitrate'):
            bitrate = controller_instance.extract_bitrate(log_line)
            
            # Assert
            assert isinstance(bitrate, (int, float))
            assert bitrate == 2516.1
        else:
            pytest.skip("extract_bitrate method not implemented yet")
    
    def test_bitrate_history_tracking(self, controller_instance):
        """测试比特率历史跟踪"""
        # Arrange
        controller_instance.bitrate_history = deque(maxlen=50)
        bitrates = [2000, 2100, 1950, 2200, 1800, 2300]
        
        # Act
        for bitrate in bitrates:
            controller_instance.bitrate_history.append(bitrate)
        
        # Assert
        assert len(controller_instance.bitrate_history) == 6
        assert list(controller_instance.bitrate_history) == bitrates
    
    def test_bitrate_statistics(self, controller_instance):
        """测试比特率统计"""
        # Arrange
        controller_instance.bitrate_history = deque([2000, 2100, 1950, 2200, 1800, 2300], maxlen=50)
        
        # Act
        if hasattr(controller_instance, 'get_bitrate_stats'):
            stats = controller_instance.get_bitrate_stats()
            
            # Assert
            assert isinstance(stats, dict)
            assert 'average' in stats
            assert 'min' in stats
            assert 'max' in stats
            assert 'current' in stats
            assert stats['min'] == 1800
            assert stats['max'] == 2300
            assert stats['current'] == 2300
            assert 1900 < stats['average'] < 2200
        else:
            pytest.skip("get_bitrate_stats method not implemented yet")
    
    def test_bitrate_alerts(self, controller_instance):
        """测试比特率警报"""
        # Arrange
        controller_instance.bitrate_history = deque(maxlen=50)
        low_bitrates = [500, 600, 400, 550]  # 异常低的比特率
        
        # Act
        for bitrate in low_bitrates:
            controller_instance.bitrate_history.append(bitrate)
        
        if hasattr(controller_instance, 'check_bitrate_alerts'):
            alerts = controller_instance.check_bitrate_alerts()
            
            # Assert
            assert isinstance(alerts, list)
            # 应该有低比特率警报
            low_bitrate_alerts = [alert for alert in alerts if 'low' in alert.get('type', '').lower()]
            assert len(low_bitrate_alerts) > 0
        else:
            pytest.skip("check_bitrate_alerts method not implemented yet")


class TestMonitoringIntegration:
    """监控集成测试"""
    
    def test_monitoring_thread_lifecycle(self, controller_instance, mock_psutil, mock_subprocess):
        """测试监控线程生命周期"""
        # Arrange
        mock_popen, mock_process = mock_subprocess
        controller_instance.current_process = mock_process
        
        # Act
        if hasattr(controller_instance, 'start_monitoring') and hasattr(controller_instance, 'stop_monitoring'):
            # 启动监控
            controller_instance.start_monitoring()
            assert controller_instance.is_monitoring is True
            assert controller_instance.monitor_thread.is_alive()
            
            # 等待一段时间
            time.sleep(0.1)
            
            # 停止监控
            controller_instance.stop_monitoring()
            assert controller_instance.is_monitoring is False
            
            # 等待线程结束
            time.sleep(0.1)
            assert not controller_instance.monitor_thread.is_alive()
        else:
            pytest.skip("monitoring methods not implemented yet")
    
    def test_monitoring_data_websocket_emission(self, controller_instance, mock_socketio, mock_psutil):
        """测试监控数据WebSocket发送"""
        # Arrange
        controller_instance.current_process = Mock()
        controller_instance.current_process.pid = 12345
        
        # Act
        if hasattr(controller_instance, 'emit_monitoring_data'):
            data = {
                'cpu_percent': 25.5,
                'memory_mb': 100,
                'timestamp': time.time()
            }
            controller_instance.emit_monitoring_data(data)
            
            # Assert
            # 检查是否通过WebSocket发送了数据
            assert len(mock_socketio.emitted_events) > 0
            event = mock_socketio.emitted_events[0]
            assert event['event'] == 'monitor_update'
            assert 'cpu_percent' in event['data']
        else:
            pytest.skip("emit_monitoring_data method not implemented yet")
    
    def test_monitoring_error_handling(self, controller_instance):
        """测试监控错误处理"""
        # Arrange
        controller_instance.current_process = Mock()
        controller_instance.current_process.pid = 12345
        
        # 模拟psutil错误
        with patch('psutil.Process') as mock_process_class:
            mock_process_class.side_effect = psutil.NoSuchProcess(12345)
            
            # Act
            if hasattr(controller_instance, 'collect_performance_data'):
                result = controller_instance.collect_performance_data()
                
                # Assert
                # 应该优雅地处理进程不存在的错误
                if isinstance(result, dict):
                    assert 'error' in result or result.get('cpu_percent') is None
            else:
                pytest.skip("collect_performance_data method not implemented yet")
    
    def test_monitoring_memory_usage(self, controller_instance, mock_psutil):
        """测试监控内存使用"""
        # Arrange
        controller_instance.current_process = Mock()
        controller_instance.current_process.pid = 12345
        
        # 模拟高内存使用
        mock_psutil.memory_info.return_value = Mock(rss=1024*1024*1024)  # 1GB
        
        # Act
        if hasattr(controller_instance, 'collect_performance_data'):
            data = controller_instance.collect_performance_data()
            
            # Assert
            assert data['memory_mb'] == 1024  # 1GB = 1024MB
        else:
            pytest.skip("collect_performance_data method not implemented yet")


class TestMonitoringConfiguration:
    """监控配置测试"""
    
    def test_monitoring_interval_configuration(self, controller_instance):
        """测试监控间隔配置"""
        # Arrange & Act
        if hasattr(controller_instance, 'set_monitoring_interval'):
            controller_instance.set_monitoring_interval(2.0)  # 2秒间隔
            
            # Assert
            assert controller_instance.monitoring_interval == 2.0
        else:
            pytest.skip("set_monitoring_interval method not implemented yet")
    
    def test_monitoring_data_retention(self, controller_instance):
        """测试监控数据保留配置"""
        # Arrange & Act
        if hasattr(controller_instance, 'set_data_retention'):
            controller_instance.set_data_retention(performance_points=200, log_lines=2000)
            
            # Assert
            assert controller_instance.performance_data.maxlen == 200
            assert controller_instance.ffmpeg_logs.maxlen == 2000
        else:
            pytest.skip("set_data_retention method not implemented yet")
    
    def test_monitoring_alerts_configuration(self, controller_instance):
        """测试监控警报配置"""
        # Arrange & Act
        if hasattr(controller_instance, 'configure_alerts'):
            config = {
                'cpu_threshold': 80,
                'memory_threshold': 500,  # MB
                'bitrate_min': 1000,
                'bitrate_max': 5000
            }
            controller_instance.configure_alerts(config)
            
            # Assert
            assert controller_instance.alert_config == config
        else:
            pytest.skip("configure_alerts method not implemented yet")
