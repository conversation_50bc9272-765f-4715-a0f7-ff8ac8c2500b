#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FFmpeg操作相关的单元测试
采用TDD方式：先写测试，再实现功能
"""

import pytest
import subprocess
import time
from unittest.mock import patch, <PERSON><PERSON>, MagicMock, call
from stream_controller import LocalFFmpegController


class TestFFmpegProcessManagement:
    """FFmpeg进程管理测试"""
    
    def test_start_push_success(self, controller_instance, sample_video_file, mock_subprocess):
        """测试成功启动推流"""
        # Arrange
        mock_popen, mock_process = mock_subprocess
        filepath = sample_video_file
        
        # Act
        result = controller_instance.start_push(filepath)
        
        # Assert
        assert result['code'] == 0
        assert '成功' in result['msg'] or 'success' in result['msg'].lower()
        assert controller_instance.current_process is not None
        assert controller_instance.current_file == filepath
        assert controller_instance.is_playing is True
        
        # 验证FFmpeg命令被正确调用
        mock_popen.assert_called_once()
        called_args = mock_popen.call_args[0][0]
        assert 'ffmpeg' in called_args
        assert filepath in called_args
    
    def test_start_push_file_not_found(self, controller_instance):
        """测试文件不存在时启动推流"""
        # Arrange
        nonexistent_file = "/path/to/nonexistent/file.mp4"
        
        # Act
        result = controller_instance.start_push(nonexistent_file)
        
        # Assert
        assert result['code'] != 0
        assert '不存在' in result['msg'] or 'not found' in result['msg'].lower()
        assert controller_instance.current_process is None
        assert controller_instance.is_playing is False
    
    def test_start_push_already_running(self, controller_instance, sample_video_file, mock_subprocess):
        """测试推流已在运行时再次启动"""
        # Arrange
        mock_popen, mock_process = mock_subprocess
        filepath = sample_video_file
        
        # 先启动一次
        controller_instance.start_push(filepath)
        
        # Act - 再次启动
        result = controller_instance.start_push(filepath)
        
        # Assert
        assert result['code'] != 0
        assert '正在运行' in result['msg'] or 'running' in result['msg'].lower()
    
    def test_stop_push_success(self, controller_instance, sample_video_file, mock_subprocess):
        """测试成功停止推流"""
        # Arrange
        mock_popen, mock_process = mock_subprocess
        filepath = sample_video_file
        
        # 先启动推流
        controller_instance.start_push(filepath)
        
        # Act
        result = controller_instance.stop_push()
        
        # Assert
        assert result['code'] == 0
        assert '停止' in result['msg'] or 'stop' in result['msg'].lower()
        assert controller_instance.current_process is None
        assert controller_instance.is_playing is False
        
        # 验证进程被正确终止
        mock_process.terminate.assert_called_once()
    
    def test_stop_push_not_running(self, controller_instance):
        """测试停止未运行的推流"""
        # Arrange - 确保没有推流在运行
        controller_instance.current_process = None
        controller_instance.is_playing = False
        
        # Act
        result = controller_instance.stop_push()
        
        # Assert
        assert result['code'] == 0
        assert '未运行' in result['msg'] or 'not running' in result['msg'].lower()
    
    def test_stop_push_force_kill(self, controller_instance, sample_video_file, mock_subprocess):
        """测试强制终止推流进程"""
        # Arrange
        mock_popen, mock_process = mock_subprocess
        mock_process.terminate.side_effect = Exception("Terminate failed")
        filepath = sample_video_file
        
        # 启动推流
        controller_instance.start_push(filepath)
        
        # Act
        result = controller_instance.stop_push()
        
        # Assert
        assert result['code'] == 0
        mock_process.kill.assert_called_once()
    
    def test_get_status_running(self, controller_instance, sample_video_file, mock_subprocess):
        """测试获取运行状态"""
        # Arrange
        mock_popen, mock_process = mock_subprocess
        filepath = sample_video_file
        
        # 启动推流
        controller_instance.start_push(filepath)
        
        # Act
        result = controller_instance.get_status()
        
        # Assert
        assert result['code'] == 0
        assert result['is_playing'] is True
        assert result['current_file'] == filepath
        assert 'pid' in result
    
    def test_get_status_not_running(self, controller_instance):
        """测试获取未运行状态"""
        # Arrange - 确保没有推流在运行
        controller_instance.current_process = None
        controller_instance.is_playing = False
        
        # Act
        result = controller_instance.get_status()
        
        # Assert
        assert result['code'] == 0
        assert result['is_playing'] is False
        assert result['current_file'] is None


class TestFFmpegCommandGeneration:
    """FFmpeg命令生成测试"""
    
    def test_build_ffmpeg_command_basic(self, controller_instance):
        """测试基本FFmpeg命令构建"""
        # Arrange
        filepath = "test_video.mp4"
        rtmp_url = "rtmp://test.example.com/live/stream"
        video_info = {
            'preset': 'veryfast',
            'sample_rate': 44100
        }
        
        # Act
        # 注意：这个方法可能需要在实际代码中实现
        if hasattr(controller_instance, '_build_ffmpeg_command'):
            cmd = controller_instance._build_ffmpeg_command(filepath, rtmp_url, video_info)
        else:
            # 如果方法不存在，我们需要先实现它
            pytest.skip("_build_ffmpeg_command method not implemented yet")
        
        # Assert
        assert isinstance(cmd, list)
        assert 'ffmpeg' in cmd
        assert filepath in cmd
        assert rtmp_url in cmd
        assert '-preset' in cmd
        assert 'veryfast' in cmd
    
    def test_build_ffmpeg_command_with_custom_settings(self, controller_instance):
        """测试自定义设置的FFmpeg命令"""
        # Arrange
        filepath = "test_video.mp4"
        rtmp_url = "rtmp://test.example.com/live/stream"
        video_info = {
            'preset': 'medium',
            'sample_rate': 48000,
            'bitrate': '2000k'
        }
        
        # Act
        if hasattr(controller_instance, '_build_ffmpeg_command'):
            cmd = controller_instance._build_ffmpeg_command(filepath, rtmp_url, video_info)
            
            # Assert
            assert 'medium' in cmd
            assert '48000' in cmd
        else:
            pytest.skip("_build_ffmpeg_command method not implemented yet")
    
    def test_ffmpeg_command_security(self, controller_instance):
        """测试FFmpeg命令的安全性"""
        # Arrange
        malicious_filepath = "test.mp4; rm -rf /"
        rtmp_url = "rtmp://test.example.com/live/stream"
        video_info = {'preset': 'veryfast'}
        
        # Act & Assert
        if hasattr(controller_instance, '_build_ffmpeg_command'):
            cmd = controller_instance._build_ffmpeg_command(malicious_filepath, rtmp_url, video_info)
            
            # 确保恶意命令被正确转义或拒绝
            cmd_str = ' '.join(cmd)
            assert '; rm -rf /' not in cmd_str
        else:
            pytest.skip("_build_ffmpeg_command method not implemented yet")


class TestVideoAnalysis:
    """视频分析测试"""
    
    def test_analyze_video_success(self, controller_instance, mock_ffprobe):
        """测试成功分析视频"""
        # Arrange
        filepath = "test_video.mp4"
        
        # Act
        if hasattr(controller_instance, 'analyze_video'):
            result = controller_instance.analyze_video(filepath)
        else:
            pytest.skip("analyze_video method not implemented yet")
        
        # Assert
        assert result['code'] == 0
        assert 'video_info' in result
        video_info = result['video_info']
        assert 'width' in video_info
        assert 'height' in video_info
        assert video_info['width'] == 1920
        assert video_info['height'] == 1080
    
    def test_analyze_video_file_not_found(self, controller_instance):
        """测试分析不存在的视频文件"""
        # Arrange
        filepath = "/nonexistent/video.mp4"
        
        # Act
        if hasattr(controller_instance, 'analyze_video'):
            result = controller_instance.analyze_video(filepath)
            
            # Assert
            assert result['code'] != 0
            assert '不存在' in result['msg'] or 'not found' in result['msg'].lower()
        else:
            pytest.skip("analyze_video method not implemented yet")
    
    def test_analyze_video_invalid_format(self, controller_instance):
        """测试分析无效格式的文件"""
        # Arrange
        filepath = "invalid_file.txt"
        
        with patch('subprocess.run') as mock_run:
            mock_result = Mock()
            mock_result.returncode = 1
            mock_result.stderr = "Invalid format"
            mock_run.return_value = mock_result
            
            # Act
            if hasattr(controller_instance, 'analyze_video'):
                result = controller_instance.analyze_video(filepath)
                
                # Assert
                assert result['code'] != 0
                assert '格式' in result['msg'] or 'format' in result['msg'].lower()
            else:
                pytest.skip("analyze_video method not implemented yet")


class TestProcessMonitoring:
    """进程监控测试"""
    
    def test_start_monitoring(self, controller_instance, mock_psutil):
        """测试开始监控"""
        # Arrange
        controller_instance.current_process = Mock()
        controller_instance.current_process.pid = 12345
        
        # Act
        if hasattr(controller_instance, 'start_monitoring'):
            controller_instance.start_monitoring()
            
            # Assert
            assert controller_instance.is_monitoring is True
            assert controller_instance.monitor_thread is not None
        else:
            pytest.skip("start_monitoring method not implemented yet")
    
    def test_stop_monitoring(self, controller_instance):
        """测试停止监控"""
        # Arrange
        controller_instance.is_monitoring = True
        controller_instance.monitor_thread = Mock()
        
        # Act
        if hasattr(controller_instance, 'stop_monitoring'):
            controller_instance.stop_monitoring()
            
            # Assert
            assert controller_instance.is_monitoring is False
        else:
            pytest.skip("stop_monitoring method not implemented yet")
    
    def test_collect_performance_data(self, controller_instance, mock_psutil):
        """测试收集性能数据"""
        # Arrange
        controller_instance.current_process = Mock()
        controller_instance.current_process.pid = 12345
        
        # Act
        if hasattr(controller_instance, 'collect_performance_data'):
            data = controller_instance.collect_performance_data()
            
            # Assert
            assert isinstance(data, dict)
            assert 'cpu_percent' in data
            assert 'memory_mb' in data
            assert 'timestamp' in data
        else:
            pytest.skip("collect_performance_data method not implemented yet")


class TestErrorHandling:
    """错误处理测试"""
    
    def test_ffmpeg_process_crash(self, controller_instance, sample_video_file):
        """测试FFmpeg进程崩溃处理"""
        # Arrange
        with patch('subprocess.Popen') as mock_popen:
            mock_process = Mock()
            mock_process.poll.return_value = 1  # 进程已退出，返回码为1
            mock_popen.return_value = mock_process
            
            # 启动推流
            controller_instance.start_push(sample_video_file)
            
            # Act - 检查状态
            result = controller_instance.get_status()
            
            # Assert
            # 应该检测到进程已崩溃
            assert result['code'] != 0 or result['is_playing'] is False
    
    def test_subprocess_exception_handling(self, controller_instance, sample_video_file):
        """测试subprocess异常处理"""
        # Arrange
        with patch('subprocess.Popen', side_effect=OSError("Command not found")):
            # Act
            result = controller_instance.start_push(sample_video_file)
            
            # Assert
            assert result['code'] != 0
            assert 'error' in result['msg'].lower() or '错误' in result['msg']
    
    def test_timeout_handling(self, controller_instance, sample_video_file):
        """测试超时处理"""
        # Arrange
        with patch('subprocess.Popen') as mock_popen:
            mock_process = Mock()
            mock_process.wait.side_effect = subprocess.TimeoutExpired("ffmpeg", 30)
            mock_popen.return_value = mock_process
            
            # 启动推流
            controller_instance.start_push(sample_video_file)
            
            # Act - 尝试停止（应该处理超时）
            result = controller_instance.stop_push()
            
            # Assert
            assert result['code'] == 0  # 应该成功处理超时
            mock_process.kill.assert_called_once()  # 应该强制终止
