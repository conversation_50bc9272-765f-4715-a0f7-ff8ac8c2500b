---
name: bug 反馈
about: 反馈 ZLMediaKit 代码本身的 bug
title: "[BUG] BUG现象描述(必填)"
labels: bug
assignees: ''

---

<!--
 请仔细阅读相关注释提示, 请务必根据提示填写相关信息.
 1. 信息不完整会影响问题的解决速度.
 1. 乱七八糟的渲染格式也会影响开发者心情, 同样会影响问题的解决. 提交前请务必点击 Preview/预览下反馈的显示效果.
 1. 不要删除模版内容, 模版的注释部分的内容不会显示，不需要删除，直接在各部分注释外面补充相关信息即可.
 -->

<!--
 markdown 语法参考:
 * https://docs.github.com/cn/get-started/writing-on-github/getting-started-with-writing-and-formatting-on-github/basic-writing-and-formatting-syntax
 * https://docs.github.com/en/get-started/writing-on-github/getting-started-with-writing-and-formatting-on-github/basic-writing-and-formatting-syntax
 -->

## 现象描述

<!--
 在使用什么功能产生的问题? 其异常表现是什么?
 如: 在测试 WebRTC 功能时, 使用 Chrome 浏览器访问 ZLMediait 自带网页播放 FFmpeg 以 RTSP 协议推送的图像有卡顿/花屏.
 -->

## 如何复现?

<!--
  明确的复现步骤对快速解决问题极有帮助.
  格式参考:
    1. 首先 ...
    1. 然后 ...
    1. 期望 ..., 结果 ...
  -->

## 相关日志或截图

<!--
  由于日志通长较长, 建议将日志信息填写到下面的 "日志内容..."

  如果是程序异常崩溃/终止, 相关调用栈信息也极为有用, 可复制下面的格式, 添加相关调用栈信息.

  替换下面的 "日志内容..." 为实际日志内容.
  -->

<details>
<summary>展开查看详细日志</summary>
<pre>

```
#详细日志粘在这里!
```
</pre>
</details>

## 配置

<!--
  部分常见问题是由于配置错误导致的, 建议仔细阅读配置文件中的注释信息

  替换下面的 "配置内容..." 为实际配置内容.
  -->

<details>
<summary>展开查看详细配置</summary>
<pre>

```ini
#config.ini内容粘在这里!
```
</pre>
</details>

## 各种环境信息

<!--
  请填写相关环境信息, 详细的环境信息有助于快速复现定位问题.

  * 代码提交记录, 可使用命令 `git rev-parse HEAD` 进行查看.
  * 操作系统及版本, 如: Windows 10, CentOS 7, ...
  * 硬件信息, 如: Intel, AMD, ARM, 飞腾, 龙芯, ...
  -->

* **代码提交记录/git commit hash**:
* **操作系统及版本**:
* **硬件信息**:
* **crash backtrace**:
```
#崩溃信息backtrace粘贴至此
```
* **其他需要补充的信息**:
