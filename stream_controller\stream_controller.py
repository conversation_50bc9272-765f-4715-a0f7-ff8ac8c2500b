#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
本地FFmpeg推流控制器
支持Web界面控制、无缝切换、多文件队列
"""

import os
import subprocess
import json
import time
import threading
import re
from collections import deque
from datetime import datetime
from flask import Flask, request, jsonify, render_template_string
from flask_socketio import SocketIO, emit
import psutil

class LocalFFmpegController:
    def __init__(self):
        self.video_dir = r"D:\Dev\ZLMediaKit\ZLMediaKit\Video-files"
        self.rtmp_url = "rtmp://127.0.0.1:1935/live/stream1"
        self.current_process = None
        self.current_file = None
        self.play_queue = []
        self.is_playing = False
        self.queue_thread = None
        
        # 监控相关属性
        self.monitor_thread = None
        self.is_monitoring = False
        self.ffmpeg_logs = deque(maxlen=1000)  # 保存最近1000条日志
        self.performance_data = deque(maxlen=100)  # 保存最近100个性能数据点
        self.start_time = None
        self.frame_count = 0
        self.bitrate_history = deque(maxlen=50)
    
    def fix_file_path(self, filepath):
        """修复文件路径格式问题"""
        if not filepath:
            return None
            
        print(f"原始路径: {repr(filepath)}")
        
        # 处理路径中可能的编码问题
        if isinstance(filepath, bytes):
            filepath = filepath.decode('utf-8', errors='ignore')
        
        # 修复可能的路径分隔符问题
        # 从错误信息看，路径变成了 "D:DevZLMediaKitZLMediaKitVideo-files	est_video1.mp4"
        # 可能是反斜杠被转义或丢失了
        
        # 尝试修复缺失的路径分隔符
        if 'D:Dev' in filepath and 'ZLMediaKit' in filepath:
            # 这是我们预期的路径格式，需要修复分隔符
            fixed_path = filepath.replace('D:Dev', 'D:\\Dev')
            fixed_path = fixed_path.replace('ZLMediaKitZLMediaKit', 'ZLMediaKit\\ZLMediaKit')
            fixed_path = fixed_path.replace('Video-files\t', 'Video-files\\')
            fixed_path = fixed_path.replace('\t', '\\')
            print(f"修复后路径: {repr(fixed_path)}")
            filepath = fixed_path
        
        # 标准化路径
        filepath = os.path.normpath(filepath)
        print(f"标准化路径: {repr(filepath)}")
        
        return filepath
        
    def get_video_files(self):
        """获取可用视频文件"""
        files = []
        print(f"扫描视频目录: {self.video_dir}")
        
        for ext in ['*.mp4', '*.avi', '*.mkv', '*.mov']:
            import glob
            pattern = os.path.join(self.video_dir, ext)
            found_files = glob.glob(pattern)
            print(f"模式 {pattern} 找到文件: {found_files}")
            files.extend(found_files)
        
        result = [{
            'name': os.path.basename(f),
            'path': f,
            'size': os.path.getsize(f)
        } for f in files]
        
        print(f"返回文件列表: {result}")
        return result
    
    def start_push(self, filepath):
        """开始推流单个文件"""
        if self.current_process and self.current_process.poll() is None:
            return {'code': -1, 'msg': '已有推流在运行'}
        
        # 修复文件路径
        filepath = self.fix_file_path(filepath)
        
        if not os.path.exists(filepath):
            # 尝试修复路径问题
            if not os.path.isabs(filepath):
                # 如果是相对路径，与视频目录拼接
                full_path = os.path.join(self.video_dir, filepath)
                full_path = os.path.normpath(full_path)
                print(f"尝试完整路径: {full_path}")
                if os.path.exists(full_path):
                    filepath = full_path
                else:
                    return {'code': -2, 'msg': f'文件不存在: {filepath}'}
            else:
                return {'code': -2, 'msg': f'文件不存在: {filepath}'}
        
        try:
            cmd = [
                'ffmpeg', '-re', '-stream_loop', '-1',
                '-i', filepath,
                '-c:v', 'libx264', '-preset', 'veryfast', '-tune', 'zerolatency',
                '-c:a', 'aac', '-ar', '44100', '-b:a', '128k',
                '-pix_fmt', 'yuv420p', '-g', '50',
                '-f', 'flv', self.rtmp_url
            ]
            
            self.current_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                creationflags=subprocess.CREATE_NEW_PROCESS_GROUP
            )
            
            self.current_file = filepath
            self.start_time = time.time()
            self.frame_count = 0
            
            # 启动监控
            self.start_monitoring()
            
            return {'code': 0, 'msg': '推流开始', 'file': os.path.basename(filepath)}
        except Exception as e:
            return {'code': -3, 'msg': str(e)}
    
    def stop_push(self):
        """停止当前推流"""
        if self.current_process and self.current_process.poll() is None:
            try:
                # 停止监控
                self.stop_monitoring()
                
                self.current_process.terminate()
                self.current_process.wait(timeout=5)
                self.current_process = None
                self.current_file = None
                self.start_time = None
                return {'code': 0, 'msg': '推流已停止'}
            except Exception as e:
                return {'code': -1, 'msg': str(e)}
        return {'code': 0, 'msg': '无运行中的推流'}
    
    def switch_video(self, new_filepath):
        """无缝切换视频"""
        # 修复文件路径
        new_filepath = self.fix_file_path(new_filepath)
        
        if not os.path.exists(new_filepath):
            # 尝试修复路径问题
            if not os.path.isabs(new_filepath):
                # 如果是相对路径，与视频目录拼接
                full_path = os.path.join(self.video_dir, new_filepath)
                full_path = os.path.normpath(full_path)
                print(f"尝试完整路径: {full_path}")
                if os.path.exists(full_path):
                    new_filepath = full_path
                else:
                    return {'code': -1, 'msg': f'文件不存在: {new_filepath}'}
            else:
                return {'code': -1, 'msg': f'文件不存在: {new_filepath}'}
        
        # 先停止当前推流
        stop_result = self.stop_push()
        if stop_result['code'] != 0:
            return stop_result
        
        # 延迟后启动新推流
        time.sleep(1)
        start_result = self.start_push(new_filepath)
        if start_result['code'] == 0:
            return {'code': 0, 'msg': '切换完成', 'file': os.path.basename(new_filepath)}
        return start_result
    
    def get_status(self):
        """获取当前状态"""
        if self.current_process and self.current_process.poll() is None:
            return {
                'code': 0,
                'is_running': True,
                'current_file': self.current_file,
                'pid': self.current_process.pid
            }
        return {
            'code': 0,
            'is_running': False,
            'current_file': None
        }
    
    def kill_all_ffmpeg(self):
        """强制停止所有ffmpeg进程"""
        try:
            for proc in psutil.process_iter(['pid', 'name']):
                if proc.info['name'] and 'ffmpeg' in proc.info['name'].lower():
                    proc.kill()
            return {'code': 0, 'msg': '所有ffmpeg进程已终止'}
        except Exception as e:
            return {'code': -1, 'msg': str(e)}
    
    def play_queue(self, files):
        """播放队列"""
        if self.is_playing:
            return {'code': -1, 'msg': '队列已在播放中'}
        
        self.play_queue = files
        self.is_playing = True
        
        def play_worker():
            for filepath in self.play_queue:
                if not self.is_playing:
                    break
                
                print(f"正在播放: {os.path.basename(filepath)}")
                result = self.switch_video(filepath)
                if result['code'] != 0:
                    print(f"播放失败: {result['msg']}")
                    break
                
                time.sleep(30)  # 播放30秒后切换下一个
            
            self.is_playing = False
        
        self.queue_thread = threading.Thread(target=play_worker)
        self.queue_thread.start()
        
        return {'code': 0, 'msg': '队列播放开始'}
    
    def start_monitoring(self):
        """开始监控FFmpeg进程"""
        if self.is_monitoring:
            return
        
        self.is_monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_worker)
        self.monitor_thread.daemon = True
        self.monitor_thread.start()
    
    def stop_monitoring(self):
        """停止监控"""
        self.is_monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=1)
    
    def _monitor_worker(self):
        """监控工作线程"""
        while self.is_monitoring:
            try:
                if self.current_process and self.current_process.poll() is None:
                    # 获取进程性能数据
                    process = psutil.Process(self.current_process.pid)
                    cpu_percent = process.cpu_percent()
                    memory_info = process.memory_info()
                    memory_mb = memory_info.rss / 1024 / 1024
                    
                    # 读取FFmpeg输出
                    self._read_ffmpeg_output()
                    
                    # 保存性能数据
                    perf_data = {
                        'timestamp': datetime.now().isoformat(),
                        'cpu_percent': cpu_percent,
                        'memory_mb': memory_mb,
                        'frame_count': self.frame_count,
                        'uptime': time.time() - self.start_time if self.start_time else 0
                    }
                    self.performance_data.append(perf_data)
                    
                    # 通过WebSocket发送实时数据
                    if hasattr(self, 'socketio'):
                        self.socketio.emit('monitor_update', perf_data)
                
                time.sleep(1)  # 每秒更新一次
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                # 进程已结束或无权限访问
                break
            except Exception as e:
                print(f"监控错误: {e}")
                break
    
    def _read_ffmpeg_output(self):
        """读取FFmpeg输出并解析"""
        if not self.current_process:
            return
        
        try:
            # 非阻塞读取stderr（FFmpeg输出到stderr）
            import select
            if hasattr(select, 'select'):
                ready, _, _ = select.select([self.current_process.stderr], [], [], 0)
                if ready:
                    line = self.current_process.stderr.readline()
                    if line:
                        self._parse_ffmpeg_log(line.decode('utf-8', errors='ignore'))
        except:
            # Windows下select不支持管道，使用其他方法
            pass
    
    def _parse_ffmpeg_log(self, line):
        """解析FFmpeg日志行"""
        timestamp = datetime.now().isoformat()
        log_entry = {
            'timestamp': timestamp,
            'content': line.strip(),
            'type': 'info'
        }
        
        # 解析帧数
        frame_match = re.search(r'frame=\s*(\d+)', line)
        if frame_match:
            self.frame_count = int(frame_match.group(1))
        
        # 解析比特率
        bitrate_match = re.search(r'bitrate=\s*([\d.]+)kbits/s', line)
        if bitrate_match:
            bitrate = float(bitrate_match.group(1))
            self.bitrate_history.append({
                'timestamp': timestamp,
                'bitrate': bitrate
            })
        
        # 检测错误
        if 'error' in line.lower() or 'failed' in line.lower():
            log_entry['type'] = 'error'
        elif 'warning' in line.lower():
            log_entry['type'] = 'warning'
        
        self.ffmpeg_logs.append(log_entry)
    
    def get_monitor_data(self):
        """获取监控数据"""
        current_status = self.get_status()
        
        # 获取最新性能数据
        latest_perf = list(self.performance_data)[-20:] if self.performance_data else []
        
        # 获取最新日志
        latest_logs = list(self.ffmpeg_logs)[-50:] if self.ffmpeg_logs else []
        
        # 获取比特率历史
        latest_bitrates = list(self.bitrate_history)[-20:] if self.bitrate_history else []
        
        return {
            'status': current_status,
            'performance': latest_perf,
            'logs': latest_logs,
            'bitrates': latest_bitrates,
            'summary': {
                'total_logs': len(self.ffmpeg_logs),
                'error_count': sum(1 for log in self.ffmpeg_logs if log.get('type') == 'error'),
                'warning_count': sum(1 for log in self.ffmpeg_logs if log.get('type') == 'warning'),
                'current_frame': self.frame_count,
                'uptime': time.time() - self.start_time if self.start_time else 0
            }
        }
    
    def clear_monitor_data(self):
        """清空监控数据"""
        self.ffmpeg_logs.clear()
        self.performance_data.clear()
        self.bitrate_history.clear()
        self.frame_count = 0
        return {'code': 0, 'msg': '监控数据已清空'}

# Flask Web应用
app = Flask(__name__)
app.config['SECRET_KEY'] = 'ffmpeg_monitor_secret_key'
socketio = SocketIO(app, cors_allowed_origins="*")
controller = LocalFFmpegController()
controller.socketio = socketio

@app.route('/')
def index():
    return render_template_string(HTML_TEMPLATE)

@app.route('/api/files')
def api_files():
    return jsonify(controller.get_video_files())

@app.route('/api/start', methods=['POST'])
def api_start():
    filepath = request.json.get('path')
    return jsonify(controller.start_push(filepath))

@app.route('/api/stop', methods=['POST'])
def api_stop():
    return jsonify(controller.stop_push())

@app.route('/api/switch', methods=['POST'])
def api_switch():
    filepath = request.json.get('path')
    return jsonify(controller.switch_video(filepath))

@app.route('/api/status')
def api_status():
    return jsonify(controller.get_status())

@app.route('/api/kill-all', methods=['POST'])
def api_kill_all():
    return jsonify(controller.kill_all_ffmpeg())

@app.route('/api/queue/play', methods=['POST'])
def api_queue_play():
    files = request.json.get('files', [])
    return jsonify(controller.play_queue(files))

@app.route('/api/queue/stop', methods=['POST'])
def api_queue_stop():
    controller.is_playing = False
    return jsonify({'code': 0, 'msg': '队列播放已停止'})

# 监控相关API
@app.route('/api/monitor/data')
def api_monitor_data():
    return jsonify(controller.get_monitor_data())

@app.route('/api/monitor/clear', methods=['POST'])
def api_monitor_clear():
    return jsonify(controller.clear_monitor_data())

@app.route('/api/monitor/start', methods=['POST'])
def api_monitor_start():
    controller.start_monitoring()
    return jsonify({'code': 0, 'msg': '监控已启动'})

@app.route('/api/monitor/stop', methods=['POST'])
def api_monitor_stop():
    controller.stop_monitoring()
    return jsonify({'code': 0, 'msg': '监控已停止'})

@app.route('/monitor')
def monitor_page():
    return render_template_string(MONITOR_HTML_TEMPLATE)

@app.route('/test')
def test_webrtc_page():
    """WebRTC播放器测试页面"""
    import os
    test_file = os.path.join(os.path.dirname(__file__), 'test_webrtc.html')
    with open(test_file, 'r', encoding='utf-8') as f:
        return f.read()

@app.route('/static/<path:filename>')
def static_files(filename):
    """提供静态文件服务"""
    import os
    from flask import send_from_directory
    static_dir = os.path.join(os.path.dirname(__file__), '.')
    return send_from_directory(static_dir, filename)

# WebRTC相关API
@app.route('/api/webrtc/play', methods=['POST'])
def api_webrtc_play():
    """获取WebRTC播放配置"""
    stream_id = request.json.get('stream', 'stream1')
    
    # ZLMediaKit WebRTC播放配置
    webrtc_config = {
        'api_url': 'http://127.0.0.1:8080/index/api/webrtc',
        'stream_url': f'http://127.0.0.1:8080/live/{stream_id}/hls.m3u8',
        'webrtc_url': f'webrtc://127.0.0.1:8080/live/{stream_id}',
        'flv_url': f'http://127.0.0.1:8080/live/{stream_id}.live.flv',
        'stream_id': stream_id,
        'app': 'live',
        'vhost': '__defaultVhost__'
    }
    
    return jsonify({
        'code': 0,
        'data': webrtc_config,
        'msg': 'WebRTC配置获取成功'
    })

@app.route('/api/stream/info')
def api_stream_info():
    """获取流信息"""
    try:
        import requests
        # 调用ZLMediaKit API获取流信息
        response = requests.get('http://127.0.0.1:8080/index/api/getMediaList', timeout=5)
        if response.status_code == 200:
            return jsonify(response.json())
        else:
            return jsonify({'code': -1, 'msg': '无法获取流信息'})
    except Exception as e:
        return jsonify({'code': -1, 'msg': f'获取流信息失败: {str(e)}'})

@app.route('/api/player/stats', methods=['POST'])
def api_player_stats():
    """接收播放器统计信息"""
    stats = request.json
    # 这里可以保存播放器统计信息用于监控
    print(f"播放器统计: {stats}")
    return jsonify({'code': 0, 'msg': '统计信息已接收'})

# WebSocket事件处理
@socketio.on('connect')
def handle_connect():
    print('客户端已连接到监控')
    emit('connected', {'data': '监控连接成功'})

@socketio.on('disconnect')
def handle_disconnect():
    print('客户端断开监控连接')

HTML_TEMPLATE = '''
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>本地FFmpeg推流控制器</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 { margin: 0; font-size: 2.5em; }
        .header p { margin: 10px 0 0 0; opacity: 0.9; }
        .content {
            padding: 30px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
        }
        .panel {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }
        .panel h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        .btn {
            padding: 12px 24px;
            margin: 5px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        .btn-primary { background: #667eea; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        .file-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .file-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: transform 0.3s;
        }
        .file-card:hover {
            transform: translateY(-3px);
        }
        .file-name {
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
            word-break: break-all;
        }
        .status {
            background: #e9ecef;
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            margin: 10px 0;
        }
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #28a745);
            transition: width 0.3s;
        }
        .video-player {
            width: 100%;
            height: 300px;
            background: #000;
            border-radius: 8px;
            object-fit: contain;
        }
        .protocol-selector {
            display: flex;
            gap: 5px;
            margin-bottom: 15px;
            justify-content: center;
        }
        .protocol-btn {
            padding: 8px 16px;
            border: 2px solid #667eea;
            background: white;
            color: #667eea;
            border-radius: 20px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s;
        }
        .protocol-btn.active {
            background: #667eea;
            color: white;
        }
        .protocol-btn:hover {
            background: #5a6fd8;
            color: white;
            border-color: #5a6fd8;
        }
        .player-info {
            margin: 15px 0;
            font-size: 12px;
            color: #666;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .stream-status {
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 11px;
            font-weight: bold;
        }
        .stream-online { 
            background: #d4edda; 
            color: #155724; 
        }
        .stream-offline { 
            background: #f8d7da; 
            color: #721c24; 
        }
        .player-stats {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
            font-size: 11px;
            font-family: monospace;
        }
        .controls {
            margin: 15px 0;
        }
        .queue-item {
            background: white;
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .full-width {
            grid-column: 1 / -1;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎬 本地FFmpeg推流控制器</h1>
            <p>零依赖 | 本地运行 | 无缝切换 | 实时控制</p>
        </div>
        
        <div class="content">
            <!-- 状态监控 -->
            <div class="panel full-width">
                <h3>📊 实时状态监控</h3>
                <div class="status" id="status">
                    正在加载...
                </div>
                <div class="controls">
                    <button class="btn btn-primary" onclick="updateStatus()">刷新状态</button>
                    <button class="btn btn-danger" onclick="killAll()">强制停止所有</button>
                    <a href="/monitor" class="btn btn-success">📊 监控面板</a>
                    <a href="/test" class="btn btn-warning">🧪 播放器测试</a>
                </div>
            </div>

            <!-- 多协议视频播放器 -->
            <div class="panel">
                <h3>📺 实时视频监控</h3>
                <div class="protocol-selector">
                    <button class="protocol-btn active" data-protocol="hls">HLS</button>
                    <button class="protocol-btn" data-protocol="flv">FLV</button>
                    <button class="protocol-btn" data-protocol="webrtc">WebRTC</button>
                </div>
                <video id="videoPlayer" class="video-player" controls muted>
                    您的浏览器不支持视频播放
                </video>
                <div class="player-info">
                    <span>协议: <span id="currentProtocol">HLS</span></span>
                    <span class="stream-status stream-offline" id="streamStatus">离线</span>
                </div>
                <div class="controls">
                    <button class="btn btn-success" onclick="startPlay()">▶️ 开始播放</button>
                    <button class="btn btn-warning" onclick="stopPlay()">⏸️ 停止播放</button>
                    <button class="btn btn-primary" onclick="refreshPlayer()">🔄 刷新</button>
                    <button class="btn btn-info" onclick="toggleMute()">🔊 静音</button>
                </div>
                <div class="player-stats" id="playerStats">
                    等待播放器数据...
                </div>
                <div class="player-debug" id="playerDebug" style="margin-top: 10px; padding: 10px; background: #fff3cd; border-radius: 5px; font-size: 11px; display: none;">
                    <strong>调试信息:</strong><br>
                    <span id="debugInfo">调试模式已开启</span>
                </div>
                <div style="text-align: center; margin-top: 10px;">
                    <button class="btn btn-warning" onclick="toggleDebug()" style="font-size: 11px; padding: 5px 10px;">🐛 调试</button>
                </div>
            </div>

            <!-- 文件管理 -->
            <div class="panel">
                <h3>📁 视频文件管理</h3>
                <div id="fileList">
                    正在加载文件列表...
                </div>
            </div>

            <!-- 推流控制 -->
            <div class="panel">
                <h3>🎮 推流控制</h3>
                <div>
                    <label>当前文件:</label>
                    <span id="currentFile">无</span>
                </div>
                <div class="controls">
                    <button class="btn btn-primary" onclick="startSelected()">开始推流</button>
                    <button class="btn btn-success" onclick="switchSelected()">无缝切换</button>
                    <button class="btn btn-danger" onclick="stopPush()">停止推流</button>
                </div>
            </div>

            <!-- 队列管理 -->
            <div class="panel full-width">
                <h3>📋 播放队列管理</h3>
                <div id="queueContainer">
                    <div class="queue-item">
                        <span>队列管理功能</span>
                        <button class="btn btn-primary" onclick="loadQueue()">加载队列</button>
                    </div>
                </div>
                <div class="controls">
                    <button class="btn btn-primary" onclick="playQueue()">播放队列</button>
                    <button class="btn btn-success" onclick="saveQueue()">保存队列</button>
                    <button class="btn btn-danger" onclick="clearQueue()">清空队列</button>
                </div>
            </div>
        </div>
    </div>

    <!-- WebRTC播放器依赖 -->
    <script src="https://cdn.jsdelivr.net/npm/hls.js@latest"></script>
    <script src="https://unpkg.com/flv.js/dist/flv.min.js"></script>
    <script src="/static/webrtc_player.js"></script>

    <script>
        const API_BASE = '/api';
        let videoFiles = [];
        let currentStatus = {};
        
        // WebRTC播放器变量
        let currentPlayer = null;
        let currentProtocol = 'hls';
        let playerStats = {
            protocol: '',
            url: '',
            resolution: '',
            fps: 0,
            bitrate: 0,
            bufferHealth: 0,
            errors: 0
        };

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadFiles();
            updateStatus();
            initPlayer();
            setInterval(updateStatus, 5000);
            setInterval(updatePlayerStats, 2000); // 每2秒更新播放器统计
            
            // 输出调试信息到控制台
            console.log('=== FFmpeg推流控制器调试信息 ===');
            console.log('WebRTC播放器类:', !!window.ZLMWebRTCPlayer);
            console.log('HLS.js:', !!window.Hls);
            console.log('FLV.js:', !!window.flvjs);
            console.log('WebRTC支持:', !!window.RTCPeerConnection);
            console.log('页面URL:', window.location.href);
            
            // 初始化播放器统计显示
            updatePlayerStatsDisplay();
        });

        // 加载文件列表
        function loadFiles() {
            console.log('开始加载文件列表...');
            const fileListEl = document.getElementById('fileList');
            fileListEl.innerHTML = '正在加载文件列表...';
            
            fetch(`${API_BASE}/files`)
                .then(response => {
                    console.log('API响应状态:', response.status);
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    return response.json();
                })
                .then(files => {
                    console.log('获取到文件列表:', files);
                    videoFiles = files;
                    const fileList = document.getElementById('fileList');
                    fileList.innerHTML = '';
                    
                    if (files.length === 0) {
                        fileList.innerHTML = '<p>未找到视频文件</p>';
                        return;
                    }

                    const fileGrid = document.createElement('div');
                    fileGrid.className = 'file-grid';
                    
                    files.forEach(file => {
                        const fileCard = document.createElement('div');
                        fileCard.className = 'file-card';
                        fileCard.innerHTML = `
                            <div class="file-name">${file.name}</div>
                            <div style="font-size: 12px; color: #666; margin-bottom: 10px;">
                                ${(file.size / 1024 / 1024).toFixed(2)} MB
                            </div>
                            <button class="btn btn-primary start-btn">开始</button>
                            <button class="btn btn-success switch-btn">切换</button>
                        `;
                        
                        // 使用事件监听器避免路径转义问题
                        const startBtn = fileCard.querySelector('.start-btn');
                        const switchBtn = fileCard.querySelector('.switch-btn');
                        
                        startBtn.addEventListener('click', () => startPush(file.path));
                        switchBtn.addEventListener('click', () => switchVideo(file.path));
                        
                        fileGrid.appendChild(fileCard);
                    });
                    
                    fileList.appendChild(fileGrid);
                })
                .catch(error => {
                    console.error('加载文件列表失败:', error);
                    document.getElementById('fileList').innerHTML = `<p>加载失败: ${error.message}</p>`;
                });
        }

        // 更新状态
        function updateStatus() {
            fetch(`${API_BASE}/status`)
                .then(response => response.json())
                .then(status => {
                    currentStatus = status;
                    const statusDiv = document.getElementById('status');
                    
                    if (status.is_running) {
                        statusDiv.innerHTML = `
                            ✅ 正在推流: ${status.current_file ? status.current_file.split('\\\\').pop() : '未知文件'}<br>
                            PID: ${status.pid}<br>
                            状态: 运行中
                        `;
                        document.getElementById('currentFile').textContent = 
                            status.current_file ? status.current_file.split('\\\\').pop() : '未知';
                    } else {
                        statusDiv.innerHTML = `
                            ❌ 未运行<br>
                            状态: 已停止
                        `;
                        document.getElementById('currentFile').textContent = '无';
                    }
                })
                .catch(error => {
                    document.getElementById('status').innerHTML = `状态获取失败: ${error.message}`;
                });
        }

        // 控制功能
        function startPush(filepath) {
            fetch(`${API_BASE}/start`, {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({path: filepath})
            })
            .then(response => response.json())
            .then(result => {
                alert(result.msg);
                updateStatus();
            });
        }

        function stopPush() {
            fetch(`${API_BASE}/stop`, {method: 'POST'})
                .then(response => response.json())
                .then(result => {
                    alert(result.msg);
                    updateStatus();
                });
        }

        function switchVideo(filepath) {
            fetch(`${API_BASE}/switch`, {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({path: filepath})
            })
            .then(response => response.json())
            .then(result => {
                alert(result.msg);
                updateStatus();
            });
        }

        function killAll() {
            fetch(`${API_BASE}/kill-all`, {method: 'POST'})
                .then(response => response.json())
                .then(result => {
                    alert(result.msg);
                    updateStatus();
                });
        }

        // 队列功能
        function playQueue() {
            const selectedFiles = Array.from(document.querySelectorAll('input[type="checkbox"]:checked'))
                .map(cb => cb.value);
            
            if (selectedFiles.length === 0) {
                alert('请先选择文件');
                return;
            }

            fetch(`${API_BASE}/queue/play`, {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({files: selectedFiles})
            })
            .then(response => response.json())
            .then(result => {
                alert(result.msg);
            });
        }

        // WebRTC播放器初始化
        function initPlayer() {
            // 协议选择器事件
            document.querySelectorAll('.protocol-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    document.querySelectorAll('.protocol-btn').forEach(b => b.classList.remove('active'));
                    this.classList.add('active');
                    currentProtocol = this.dataset.protocol;
                    document.getElementById('currentProtocol').textContent = currentProtocol.toUpperCase();
                    stopPlay(); // 切换协议时停止当前播放
                });
            });
        }
        
        async function startPlay() {
            const video = document.getElementById('videoPlayer');
            const streamId = 'stream1';
            
            await stopPlay(); // 先停止当前播放
            
            try {
                switch(currentProtocol) {
                    case 'hls':
                        await startHLSPlay(video, streamId);
                        break;
                    case 'flv':
                        await startFLVPlay(video, streamId);
                        break;
                    case 'webrtc':
                        await startWebRTCPlay(video, streamId);
                        break;
                }
                
                updateStreamStatus('online');
                updatePlayerStats();
            } catch (error) {
                console.error('播放启动失败:', error);
                updateStreamStatus('offline');
                alert(`播放失败: ${error.message}`);
            }
        }
        
        async function startHLSPlay(video, streamId) {
            const hlsUrl = `http://127.0.0.1:8080/live/${streamId}/hls.m3u8`;
            playerStats.url = hlsUrl;
            playerStats.protocol = 'HLS';
            
            if (Hls.isSupported()) {
                currentPlayer = new Hls({
                    debug: false,
                    enableWorker: true,
                    lowLatencyMode: true
                });
                
                currentPlayer.loadSource(hlsUrl);
                currentPlayer.attachMedia(video);
                
                currentPlayer.on(Hls.Events.MANIFEST_PARSED, function() {
                    video.play();
                });
                
                currentPlayer.on(Hls.Events.ERROR, function(event, data) {
                    console.error('HLS错误:', data);
                    playerStats.errors++;
                    if (data.fatal) {
                        updateStreamStatus('offline');
                    }
                });
                
            } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
                video.src = hlsUrl;
                video.play();
            } else {
                throw new Error('浏览器不支持HLS播放');
            }
        }
        
        async function startFLVPlay(video, streamId) {
            const flvUrl = `http://127.0.0.1:8080/live/${streamId}.live.flv`;
            playerStats.url = flvUrl;
            playerStats.protocol = 'FLV';
            
            if (flvjs.isSupported()) {
                currentPlayer = flvjs.createPlayer({
                    type: 'flv',
                    url: flvUrl,
                    isLive: true,
                    hasAudio: true,
                    hasVideo: true
                });
                
                currentPlayer.attachMediaElement(video);
                currentPlayer.load();
                
                currentPlayer.on(flvjs.Events.LOADING_COMPLETE, function() {
                    video.play();
                });
                
                currentPlayer.on(flvjs.Events.ERROR, function(errorType, errorDetail) {
                    console.error('FLV错误:', errorType, errorDetail);
                    playerStats.errors++;
                    updateStreamStatus('offline');
                });
                
            } else {
                throw new Error('浏览器不支持FLV播放');
            }
        }
        
        async function startWebRTCPlay(video, streamId) {
            playerStats.protocol = 'WebRTC';
            playerStats.url = `webrtc://127.0.0.1:8080/live/${streamId}`;
            
            try {
                if (!window.RTCPeerConnection) {
                    throw new Error('浏览器不支持WebRTC');
                }
                
                if (window.ZLMWebRTCPlayer) {
                    currentPlayer = new ZLMWebRTCPlayer(video, {
                        stream: streamId,
                        app: 'live',
                        vhost: '__defaultVhost__'
                    });
                    
                    currentPlayer.setErrorCallback((error) => {
                        console.error('WebRTC播放错误:', error);
                        updateStreamStatus('offline');
                        playerStats.errors++;
                    });
                    
                    await currentPlayer.play();
                    console.log('WebRTC播放启动成功');
                    
                } else {
                    throw new Error('WebRTC播放器未加载');
                }
            } catch (error) {
                console.error('WebRTC播放失败:', error);
                
                // 降级到HLS播放
                console.log('降级到HLS播放');
                alert(`WebRTC播放失败: ${error.message}\n自动切换到HLS播放`);
                
                currentProtocol = 'hls';
                document.querySelector('[data-protocol="hls"]').classList.add('active');
                document.querySelector('[data-protocol="webrtc"]').classList.remove('active');
                document.getElementById('currentProtocol').textContent = 'HLS (WebRTC降级)';
                
                await startHLSPlay(video, streamId);
            }
        }
        
        async function stopPlay() {
            const video = document.getElementById('videoPlayer');
            
            if (currentPlayer) {
                try {
                    if (currentPlayer.stop) {
                        await currentPlayer.stop();
                    } else if (currentPlayer.destroy) {
                        currentPlayer.destroy();
                    } else if (currentPlayer.unload) {
                        currentPlayer.unload();
                    }
                } catch (e) {
                    console.warn('播放器清理失败:', e);
                }
                currentPlayer = null;
            }
            
            video.src = '';
            video.srcObject = null;
            video.load();
            
            updateStreamStatus('offline');
            playerStats = {
                protocol: currentProtocol.toUpperCase(),
                url: '',
                resolution: '',
                fps: 0,
                bitrate: 0,
                bufferHealth: 0,
                errors: 0
            };
            updatePlayerStatsDisplay();
        }
        
        async function refreshPlayer() {
            if (currentPlayer) {
                await stopPlay();
                setTimeout(async () => await startPlay(), 1000);
            }
        }
        
        function toggleMute() {
            const video = document.getElementById('videoPlayer');
            video.muted = !video.muted;
            document.querySelector('[onclick="toggleMute()"]').textContent = 
                video.muted ? '🔇 取消静音' : '🔊 静音';
        }
        
        function updateStreamStatus(status) {
            const statusEl = document.getElementById('streamStatus');
            statusEl.textContent = status === 'online' ? '在线' : '离线';
            statusEl.className = `stream-status stream-${status}`;
        }
        
        function updatePlayerStats() {
            if (!currentPlayer) return;
            
            const video = document.getElementById('videoPlayer');
            
            if (video.videoWidth && video.videoHeight) {
                playerStats.resolution = `${video.videoWidth}x${video.videoHeight}`;
            }
            
            if (video.buffered.length > 0) {
                const buffered = video.buffered.end(video.buffered.length - 1);
                const currentTime = video.currentTime;
                playerStats.bufferHealth = Math.max(0, buffered - currentTime);
            }
            
            updatePlayerStatsDisplay();
        }
        
        function updatePlayerStatsDisplay() {
            const statsEl = document.getElementById('playerStats');
            statsEl.innerHTML = `
                协议: ${playerStats.protocol}<br>
                URL: ${playerStats.url}<br>
                分辨率: ${playerStats.resolution || '未知'}<br>
                帧率: ${playerStats.fps.toFixed(1)} FPS<br>
                比特率: ${playerStats.bitrate.toFixed(0)} kbps<br>
                缓冲: ${playerStats.bufferHealth.toFixed(1)}s<br>
                错误: ${playerStats.errors}
            `;
        }
        
        function toggleDebug() {
            const debugDiv = document.getElementById('playerDebug');
            debugDiv.style.display = debugDiv.style.display === 'none' ? 'block' : 'none';
            
            if (debugDiv.style.display === 'block') {
                updateDebugInfo();
            }
        }
        
        function updateDebugInfo() {
            const debugInfo = document.getElementById('debugInfo');
            const info = [];
            
            info.push(`WebRTC播放器类: ${!!window.ZLMWebRTCPlayer ? '✅' : '❌'}`);
            info.push(`HLS.js: ${!!window.Hls ? '✅' : '❌'}`);
            info.push(`FLV.js: ${!!window.flvjs ? '✅' : '❌'}`);
            info.push(`WebRTC支持: ${!!window.RTCPeerConnection ? '✅' : '❌'}`);
            info.push(`当前协议: ${currentProtocol}`);
            info.push(`播放器实例: ${currentPlayer ? '有' : '无'}`);
            info.push(`浏览器: ${navigator.userAgent.split(' ')[0]}`);
            
            debugInfo.innerHTML = info.join('<br>');
        }
    </script>
</body>
</html>
'''

MONITOR_HTML_TEMPLATE = '''
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FFmpeg实时监控面板</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/hls.js@latest"></script>
    <script src="https://unpkg.com/flv.js/dist/flv.min.js"></script>
    <script src="/static/webrtc_player.js"></script>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: #333;
            min-height: 100vh;
        }
        .header {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            padding: 20px;
            color: white;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.2);
        }
        .header h1 { font-size: 2.5em; margin-bottom: 10px; }
        .header p { opacity: 0.9; font-size: 1.1em; }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .panel {
            background: rgba(255,255,255,0.95);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            backdrop-filter: blur(10px);
        }
        .panel h3 {
            color: #2a5298;
            margin-bottom: 20px;
            font-size: 1.3em;
            border-bottom: 2px solid #2a5298;
            padding-bottom: 10px;
        }
        .full-width { grid-column: 1 / -1; }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        .status-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }
        .status-card h4 { font-size: 0.9em; opacity: 0.9; margin-bottom: 10px; }
        .status-card .value { font-size: 2em; font-weight: bold; }
        .status-card .unit { font-size: 0.8em; opacity: 0.8; }
        .chart-container {
            position: relative;
            height: 300px;
            margin: 20px 0;
        }
        .log-container {
            height: 400px;
            overflow-y: auto;
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        .log-entry {
            margin-bottom: 5px;
            padding: 5px;
            border-radius: 3px;
        }
        .log-info { background: #e8f4fd; color: #0c5460; }
        .log-warning { background: #fff3cd; color: #856404; }
        .log-error { background: #f8d7da; color: #721c24; }
        .log-timestamp { color: #6c757d; font-size: 11px; }
        .btn {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
        }
        .btn-primary { background: #2a5298; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        .btn:hover { transform: translateY(-2px); box-shadow: 0 4px 12px rgba(0,0,0,0.15); }
        .controls { margin-bottom: 20px; text-align: center; }
        .connection-status {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 15px;
            border-radius: 20px;
            color: white;
            font-size: 12px;
            z-index: 1000;
        }
        .connected { background: #28a745; }
        .disconnected { background: #dc3545; }
        .three-column { grid-template-columns: 1fr 1fr 1fr; }
        .performance-summary {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
        }
        .perf-item {
            text-align: center;
            flex: 1;
        }
        .perf-value { font-size: 1.5em; font-weight: bold; color: #2a5298; }
        .perf-label { font-size: 0.9em; color: #666; }
        .video-player {
            width: 100%;
            height: 300px;
            background: #000;
            border-radius: 8px;
            object-fit: contain;
        }
        .player-controls {
            margin-top: 15px;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            justify-content: center;
        }
        .protocol-selector {
            display: flex;
            gap: 5px;
            margin-bottom: 10px;
        }
        .protocol-btn {
            padding: 5px 15px;
            border: 2px solid #2a5298;
            background: white;
            color: #2a5298;
            border-radius: 20px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s;
        }
        .protocol-btn.active {
            background: #2a5298;
            color: white;
        }
        .protocol-btn:hover {
            background: #1e3c72;
            color: white;
            border-color: #1e3c72;
        }
        .player-info {
            margin-top: 10px;
            font-size: 12px;
            color: #666;
            display: flex;
            justify-content: space-between;
        }
        .stream-status {
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 11px;
            font-weight: bold;
        }
        .stream-online { background: #d4edda; color: #155724; }
        .stream-offline { background: #f8d7da; color: #721c24; }
        .player-stats {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
            font-size: 11px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="connection-status" id="connectionStatus">🔴 未连接</div>
    
    <div class="header">
        <h1>📊 FFmpeg实时监控面板</h1>
        <p>实时监控推流状态 | 性能分析 | 日志跟踪</p>
    </div>

    <div class="container">
        <!-- 状态概览 -->
        <div class="panel full-width">
            <h3>📈 实时状态概览</h3>
            <div class="status-grid">
                <div class="status-card">
                    <h4>推流状态</h4>
                    <div class="value" id="streamStatus">停止</div>
                </div>
                <div class="status-card">
                    <h4>运行时间</h4>
                    <div class="value" id="uptime">0</div>
                    <div class="unit">秒</div>
                </div>
                <div class="status-card">
                    <h4>处理帧数</h4>
                    <div class="value" id="frameCount">0</div>
                    <div class="unit">帧</div>
                </div>
                <div class="status-card">
                    <h4>CPU使用率</h4>
                    <div class="value" id="cpuUsage">0</div>
                    <div class="unit">%</div>
                </div>
                <div class="status-card">
                    <h4>内存使用</h4>
                    <div class="value" id="memoryUsage">0</div>
                    <div class="unit">MB</div>
                </div>
                <div class="status-card">
                    <h4>错误计数</h4>
                    <div class="value" id="errorCount">0</div>
                    <div class="unit">个</div>
                </div>
            </div>
            <div class="controls">
                <button class="btn btn-primary" onclick="refreshData()">🔄 刷新数据</button>
                <button class="btn btn-success" onclick="startMonitor()">▶️ 开始监控</button>
                <button class="btn btn-warning" onclick="stopMonitor()">⏸️ 停止监控</button>
                <button class="btn btn-danger" onclick="clearData()">🗑️ 清空数据</button>
                <a href="/" class="btn btn-primary">🏠 返回主页</a>
            </div>
        </div>

        <!-- 视频播放器 -->
        <div class="panel">
            <h3>📺 实时视频监控</h3>
            <div class="protocol-selector">
                <button class="protocol-btn active" data-protocol="hls">HLS</button>
                <button class="protocol-btn" data-protocol="flv">FLV</button>
                <button class="protocol-btn" data-protocol="webrtc">WebRTC</button>
            </div>
            <video id="videoPlayer" class="video-player" controls muted>
                您的浏览器不支持视频播放
            </video>
            <div class="player-info">
                <span>协议: <span id="currentProtocol">HLS</span></span>
                <span class="stream-status stream-offline" id="streamStatus">离线</span>
            </div>
            <div class="player-controls">
                <button class="btn btn-success" onclick="startPlay()">▶️ 开始播放</button>
                <button class="btn btn-warning" onclick="stopPlay()">⏸️ 停止播放</button>
                <button class="btn btn-primary" onclick="refreshPlayer()">🔄 刷新</button>
                <button class="btn btn-info" onclick="toggleMute()">🔊 静音</button>
            </div>
            <div class="player-stats" id="playerStats">
                等待播放器数据...
            </div>
            <div class="player-debug" id="playerDebug" style="margin-top: 10px; padding: 10px; background: #fff3cd; border-radius: 5px; font-size: 11px; display: none;">
                <strong>调试信息:</strong><br>
                <span id="debugInfo">调试模式已开启</span>
            </div>
            <div style="text-align: center; margin-top: 10px;">
                <button class="btn btn-warning" onclick="toggleDebug()" style="font-size: 11px; padding: 5px 10px;">🐛 调试</button>
            </div>
        </div>

        <!-- 性能图表 -->
        <div class="panel">
            <h3>⚡ CPU & 内存使用率</h3>
            <div class="chart-container">
                <canvas id="performanceChart"></canvas>
            </div>
        </div>

        <!-- 比特率图表 -->
        <div class="panel">
            <h3>📊 比特率监控</h3>
            <div class="chart-container">
                <canvas id="bitrateChart"></canvas>
            </div>
        </div>

        <!-- 实时日志 -->
        <div class="panel full-width">
            <h3>📝 FFmpeg实时日志</h3>
            <div class="performance-summary">
                <div class="perf-item">
                    <div class="perf-value" id="totalLogs">0</div>
                    <div class="perf-label">总日志数</div>
                </div>
                <div class="perf-item">
                    <div class="perf-value" id="warningCount">0</div>
                    <div class="perf-label">警告数</div>
                </div>
                <div class="perf-item">
                    <div class="perf-value" id="errorLogCount">0</div>
                    <div class="perf-label">错误数</div>
                </div>
            </div>
            <div class="log-container" id="logContainer">
                <div class="log-entry log-info">
                    <span class="log-timestamp">[等待数据]</span> 监控系统已就绪，等待FFmpeg进程启动...
                </div>
            </div>
        </div>
    </div>

    <script>
        // WebSocket连接
        const socket = io();
        let performanceChart, bitrateChart;
        
        // 播放器相关变量
        let currentPlayer = null;
        let currentProtocol = 'hls';
        let playerStats = {
            protocol: '',
            url: '',
            resolution: '',
            fps: 0,
            bitrate: 0,
            bufferHealth: 0,
            errors: 0
        };
        
        // 连接状态管理
        socket.on('connect', function() {
            document.getElementById('connectionStatus').innerHTML = '🟢 已连接';
            document.getElementById('connectionStatus').className = 'connection-status connected';
        });
        
        socket.on('disconnect', function() {
            document.getElementById('connectionStatus').innerHTML = '🔴 未连接';
            document.getElementById('connectionStatus').className = 'connection-status disconnected';
        });
        
        // 实时数据更新
        socket.on('monitor_update', function(data) {
            updateStatusCards(data);
            updateCharts(data);
        });
        
        // 初始化图表
        function initCharts() {
            // 性能图表
            const perfCtx = document.getElementById('performanceChart').getContext('2d');
            performanceChart = new Chart(perfCtx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: 'CPU使用率 (%)',
                        data: [],
                        borderColor: '#ff6384',
                        backgroundColor: 'rgba(255, 99, 132, 0.1)',
                        tension: 0.4
                    }, {
                        label: '内存使用 (MB)',
                        data: [],
                        borderColor: '#36a2eb',
                        backgroundColor: 'rgba(54, 162, 235, 0.1)',
                        tension: 0.4,
                        yAxisID: 'y1'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            title: { display: true, text: 'CPU (%)' }
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            title: { display: true, text: '内存 (MB)' },
                            grid: { drawOnChartArea: false }
                        }
                    },
                    plugins: {
                        legend: { display: true },
                        title: { display: true, text: '实时性能监控' }
                    }
                }
            });
            
            // 比特率图表
            const bitrateCtx = document.getElementById('bitrateChart').getContext('2d');
            bitrateChart = new Chart(bitrateCtx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: '比特率 (kbps)',
                        data: [],
                        borderColor: '#4bc0c0',
                        backgroundColor: 'rgba(75, 192, 192, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            title: { display: true, text: '比特率 (kbps)' }
                        }
                    },
                    plugins: {
                        legend: { display: true },
                        title: { display: true, text: '推流比特率监控' }
                    }
                }
            });
        }
        
        // 更新状态卡片
        function updateStatusCards(data) {
            if (data.cpu_percent !== undefined) {
                document.getElementById('cpuUsage').textContent = data.cpu_percent.toFixed(1);
            }
            if (data.memory_mb !== undefined) {
                document.getElementById('memoryUsage').textContent = Math.round(data.memory_mb);
            }
            if (data.frame_count !== undefined) {
                document.getElementById('frameCount').textContent = data.frame_count;
            }
            if (data.uptime !== undefined) {
                document.getElementById('uptime').textContent = Math.round(data.uptime);
            }
        }
        
        // 更新图表
        function updateCharts(data) {
            const now = new Date().toLocaleTimeString();
            
            // 更新性能图表
            if (performanceChart && data.cpu_percent !== undefined) {
                performanceChart.data.labels.push(now);
                performanceChart.data.datasets[0].data.push(data.cpu_percent);
                performanceChart.data.datasets[1].data.push(data.memory_mb);
                
                // 保持最近20个数据点
                if (performanceChart.data.labels.length > 20) {
                    performanceChart.data.labels.shift();
                    performanceChart.data.datasets[0].data.shift();
                    performanceChart.data.datasets[1].data.shift();
                }
                performanceChart.update('none');
            }
        }
        
        // 刷新数据
        function refreshData() {
            fetch('/api/monitor/data')
                .then(response => response.json())
                .then(data => {
                    updateStatusDisplay(data);
                    updateLogDisplay(data.logs || []);
                    updateBitrateChart(data.bitrates || []);
                })
                .catch(error => console.error('获取数据失败:', error));
        }
        
        // 更新状态显示
        function updateStatusDisplay(data) {
            const status = data.status || {};
            const summary = data.summary || {};
            
            document.getElementById('streamStatus').textContent = status.is_running ? '运行中' : '停止';
            document.getElementById('frameCount').textContent = summary.current_frame || 0;
            document.getElementById('uptime').textContent = Math.round(summary.uptime || 0);
            document.getElementById('errorCount').textContent = summary.error_count || 0;
            document.getElementById('totalLogs').textContent = summary.total_logs || 0;
            document.getElementById('warningCount').textContent = summary.warning_count || 0;
            document.getElementById('errorLogCount').textContent = summary.error_count || 0;
        }
        
        // 更新日志显示
        function updateLogDisplay(logs) {
            const container = document.getElementById('logContainer');
            container.innerHTML = '';
            
            logs.forEach(log => {
                const div = document.createElement('div');
                div.className = `log-entry log-${log.type || 'info'}`;
                div.innerHTML = `
                    <span class="log-timestamp">[${new Date(log.timestamp).toLocaleTimeString()}]</span>
                    ${log.content}
                `;
                container.appendChild(div);
            });
            
            container.scrollTop = container.scrollHeight;
        }
        
        // 更新比特率图表
        function updateBitrateChart(bitrates) {
            if (!bitrateChart) return;
            
            bitrateChart.data.labels = bitrates.map(b => new Date(b.timestamp).toLocaleTimeString());
            bitrateChart.data.datasets[0].data = bitrates.map(b => b.bitrate);
            bitrateChart.update();
        }
        
        // 控制函数
        function startMonitor() {
            fetch('/api/monitor/start', { method: 'POST' })
                .then(response => response.json())
                .then(data => alert(data.msg));
        }
        
        function stopMonitor() {
            fetch('/api/monitor/stop', { method: 'POST' })
                .then(response => response.json())
                .then(data => alert(data.msg));
        }
        
        function clearData() {
            if (confirm('确定要清空所有监控数据吗？')) {
                fetch('/api/monitor/clear', { method: 'POST' })
                    .then(response => response.json())
                    .then(data => {
                        alert(data.msg);
                        refreshData();
                    });
            }
        }
        
        // 播放器功能
        function initPlayer() {
            // 协议选择器事件
            document.querySelectorAll('.protocol-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    document.querySelectorAll('.protocol-btn').forEach(b => b.classList.remove('active'));
                    this.classList.add('active');
                    currentProtocol = this.dataset.protocol;
                    document.getElementById('currentProtocol').textContent = currentProtocol.toUpperCase();
                    stopPlay(); // 切换协议时停止当前播放
                });
            });
        }
        
        async function startPlay() {
            const video = document.getElementById('videoPlayer');
            const streamId = 'stream1'; // 默认流ID
            
            await stopPlay(); // 先停止当前播放
            
            try {
                switch(currentProtocol) {
                    case 'hls':
                        await startHLSPlay(video, streamId);
                        break;
                    case 'flv':
                        await startFLVPlay(video, streamId);
                        break;
                    case 'webrtc':
                        await startWebRTCPlay(video, streamId);
                        break;
                }
                
                updateStreamStatus('online');
                updatePlayerStats();
            } catch (error) {
                console.error('播放启动失败:', error);
                updateStreamStatus('offline');
                alert(`播放失败: ${error.message}`);
            }
        }
        
        async function startHLSPlay(video, streamId) {
            const hlsUrl = `http://127.0.0.1:8080/live/${streamId}/hls.m3u8`;
            playerStats.url = hlsUrl;
            playerStats.protocol = 'HLS';
            
            if (Hls.isSupported()) {
                currentPlayer = new Hls({
                    debug: false,
                    enableWorker: true,
                    lowLatencyMode: true
                });
                
                currentPlayer.loadSource(hlsUrl);
                currentPlayer.attachMedia(video);
                
                currentPlayer.on(Hls.Events.MANIFEST_PARSED, function() {
                    video.play();
                });
                
                currentPlayer.on(Hls.Events.ERROR, function(event, data) {
                    console.error('HLS错误:', data);
                    playerStats.errors++;
                    if (data.fatal) {
                        updateStreamStatus('offline');
                    }
                });
                
                currentPlayer.on(Hls.Events.LEVEL_LOADED, function(event, data) {
                    playerStats.bitrate = data.details.totalduration > 0 ? 
                        (data.details.totalBytes * 8 / data.details.totalduration / 1000) : 0;
                });
                
            } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
                // Safari原生支持
                video.src = hlsUrl;
                video.play();
            } else {
                throw new Error('浏览器不支持HLS播放');
            }
        }
        
        async function startFLVPlay(video, streamId) {
            const flvUrl = `http://127.0.0.1:8080/live/${streamId}.live.flv`;
            playerStats.url = flvUrl;
            playerStats.protocol = 'FLV';
            
            if (flvjs.isSupported()) {
                currentPlayer = flvjs.createPlayer({
                    type: 'flv',
                    url: flvUrl,
                    isLive: true,
                    hasAudio: true,
                    hasVideo: true
                }, {
                    enableWorker: false,
                    enableStashBuffer: false,
                    stashInitialSize: 128,
                    lazyLoad: false,
                    autoCleanupSourceBuffer: true
                });
                
                currentPlayer.attachMediaElement(video);
                currentPlayer.load();
                
                currentPlayer.on(flvjs.Events.LOADING_COMPLETE, function() {
                    video.play();
                });
                
                currentPlayer.on(flvjs.Events.ERROR, function(errorType, errorDetail) {
                    console.error('FLV错误:', errorType, errorDetail);
                    playerStats.errors++;
                    updateStreamStatus('offline');
                });
                
                currentPlayer.on(flvjs.Events.STATISTICS_INFO, function(res) {
                    playerStats.fps = res.fps || 0;
                    playerStats.bitrate = res.speed || 0;
                });
                
            } else {
                throw new Error('浏览器不支持FLV播放');
            }
        }
        
        async function startWebRTCPlay(video, streamId) {
            playerStats.protocol = 'WebRTC';
            playerStats.url = `webrtc://127.0.0.1:8080/live/${streamId}`;
            
            try {
                // 检查是否支持WebRTC
                if (!window.RTCPeerConnection) {
                    throw new Error('浏览器不支持WebRTC');
                }
                
                // 创建WebRTC播放器实例
                if (window.ZLMWebRTCPlayer) {
                    currentPlayer = new ZLMWebRTCPlayer(video, {
                        stream: streamId,
                        app: 'live',
                        vhost: '__defaultVhost__'
                    });
                    
                    // 设置错误回调
                    currentPlayer.setErrorCallback((error) => {
                        console.error('WebRTC播放错误:', error);
                        updateStreamStatus('offline');
                        playerStats.errors++;
                    });
                    
                    // 开始播放
                    await currentPlayer.play();
                    console.log('WebRTC播放启动成功');
                    
                    // 定期更新统计信息
                    if (window.webrtcStatsInterval) {
                        clearInterval(window.webrtcStatsInterval);
                    }
                    
                    window.webrtcStatsInterval = setInterval(async () => {
                        if (currentPlayer && currentPlayer.isPlaying) {
                            const stats = await currentPlayer.getStats();
                            playerStats.fps = stats.fps;
                            playerStats.bitrate = stats.bitrate;
                            playerStats.resolution = stats.resolution;
                            updatePlayerStatsDisplay();
                        }
                    }, 2000);
                    
                } else {
                    throw new Error('WebRTC播放器未加载');
                }
            } catch (error) {
                console.error('WebRTC播放失败:', error);
                
                // 降级到HLS播放
                console.log('降级到HLS播放');
                alert(`WebRTC播放失败: ${error.message}\n自动切换到HLS播放`);
                
                // 切换到HLS
                currentProtocol = 'hls';
                document.querySelector('[data-protocol="hls"]').classList.add('active');
                document.querySelector('[data-protocol="webrtc"]').classList.remove('active');
                document.getElementById('currentProtocol').textContent = 'HLS (WebRTC降级)';
                
                await startHLSPlay(video, streamId);
            }
        }
        
        async function stopPlay() {
            const video = document.getElementById('videoPlayer');
            
            // 清理WebRTC统计定时器
            if (window.webrtcStatsInterval) {
                clearInterval(window.webrtcStatsInterval);
                window.webrtcStatsInterval = null;
            }
            
            if (currentPlayer) {
                try {
                    if (currentPlayer.stop) {
                        // WebRTC播放器
                        await currentPlayer.stop();
                    } else if (currentPlayer.destroy) {
                        // HLS播放器
                        currentPlayer.destroy();
                    } else if (currentPlayer.unload) {
                        // FLV播放器
                        currentPlayer.unload();
                    }
                } catch (e) {
                    console.warn('播放器清理失败:', e);
                }
                currentPlayer = null;
            }
            
            // 清空视频源
            video.src = '';
            video.srcObject = null;
            video.load();
            
            updateStreamStatus('offline');
            playerStats = {
                protocol: currentProtocol.toUpperCase(),
                url: '',
                resolution: '',
                fps: 0,
                bitrate: 0,
                bufferHealth: 0,
                errors: 0
            };
            updatePlayerStatsDisplay();
        }
        
        async function refreshPlayer() {
            if (currentPlayer) {
                await stopPlay();
                setTimeout(async () => await startPlay(), 1000);
            }
        }
        
        function toggleMute() {
            const video = document.getElementById('videoPlayer');
            video.muted = !video.muted;
            document.querySelector('[onclick="toggleMute()"]').textContent = 
                video.muted ? '🔇 取消静音' : '🔊 静音';
        }
        
        function updateStreamStatus(status) {
            const statusEl = document.getElementById('streamStatus');
            statusEl.textContent = status === 'online' ? '在线' : '离线';
            statusEl.className = `stream-status stream-${status}`;
        }
        
        function updatePlayerStats() {
            if (!currentPlayer) return;
            
            const video = document.getElementById('videoPlayer');
            
            // 更新分辨率
            if (video.videoWidth && video.videoHeight) {
                playerStats.resolution = `${video.videoWidth}x${video.videoHeight}`;
            }
            
            // 更新缓冲健康度
            if (video.buffered.length > 0) {
                const buffered = video.buffered.end(video.buffered.length - 1);
                const currentTime = video.currentTime;
                playerStats.bufferHealth = Math.max(0, buffered - currentTime);
            }
            
            updatePlayerStatsDisplay();
        }
        
        function updatePlayerStatsDisplay() {
            const statsEl = document.getElementById('playerStats');
            statsEl.innerHTML = `
                协议: ${playerStats.protocol}<br>
                URL: ${playerStats.url}<br>
                分辨率: ${playerStats.resolution || '未知'}<br>
                帧率: ${playerStats.fps.toFixed(1)} FPS<br>
                比特率: ${playerStats.bitrate.toFixed(0)} kbps<br>
                缓冲: ${playerStats.bufferHealth.toFixed(1)}s<br>
                错误: ${playerStats.errors}
            `;
        }

        // 调试功能
        function toggleDebug() {
            const debugDiv = document.getElementById('playerDebug');
            debugDiv.style.display = debugDiv.style.display === 'none' ? 'block' : 'none';
            
            if (debugDiv.style.display === 'block') {
                updateDebugInfo();
            }
        }
        
        function updateDebugInfo() {
            const debugInfo = document.getElementById('debugInfo');
            const info = [];
            
            info.push(`WebRTC播放器类: ${!!window.ZLMWebRTCPlayer ? '✅' : '❌'}`);
            info.push(`HLS.js: ${!!window.Hls ? '✅' : '❌'}`);
            info.push(`FLV.js: ${!!window.flvjs ? '✅' : '❌'}`);
            info.push(`WebRTC支持: ${!!window.RTCPeerConnection ? '✅' : '❌'}`);
            info.push(`当前协议: ${currentProtocol}`);
            info.push(`播放器实例: ${currentPlayer ? '有' : '无'}`);
            info.push(`浏览器: ${navigator.userAgent.split(' ')[0]}`);
            
            debugInfo.innerHTML = info.join('<br>');
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initCharts();
            initPlayer();
            refreshData();
            setInterval(refreshData, 5000); // 每5秒刷新一次
            setInterval(updatePlayerStats, 2000); // 每2秒更新播放器统计
            
            // 输出调试信息到控制台
            console.log('=== FFmpeg监控面板调试信息 ===');
            console.log('WebRTC播放器类:', !!window.ZLMWebRTCPlayer);
            console.log('HLS.js:', !!window.Hls);
            console.log('FLV.js:', !!window.flvjs);
            console.log('WebRTC支持:', !!window.RTCPeerConnection);
            console.log('页面URL:', window.location.href);
        });
    </script>
</body>
</html>
'''

if __name__ == '__main__':
    print("本地FFmpeg推流控制器已启动")
    print("访问地址: http://127.0.0.1:5000")
    # 修复Windows下debug模式的模块导入问题
    # 使用SocketIO运行，支持WebSocket实时通信
    socketio.run(app, host='0.0.0.0', port=5000, debug=False)