#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件操作相关的单元测试
采用TDD方式：先写测试，再实现功能
"""

import pytest
import os
import tempfile
from unittest.mock import patch, Mock
from stream_controller import LocalFFmpegController


class TestFileOperations:
    """文件操作测试类"""
    
    def test_fix_file_path_with_valid_path(self, controller_instance):
        """测试修复有效文件路径"""
        # Arrange
        test_path = "test_video.mp4"
        
        # Act
        result = controller_instance.fix_file_path(test_path)
        
        # Assert
        assert result is not None
        assert isinstance(result, str)
        assert result.endswith("test_video.mp4")
    
    def test_fix_file_path_with_empty_path(self, controller_instance):
        """测试空路径处理"""
        # Arrange
        test_path = ""
        
        # Act
        result = controller_instance.fix_file_path(test_path)
        
        # Assert
        assert result is None
    
    def test_fix_file_path_with_none(self, controller_instance):
        """测试None路径处理"""
        # Arrange
        test_path = None
        
        # Act
        result = controller_instance.fix_file_path(test_path)
        
        # Assert
        assert result is None
    
    def test_fix_file_path_with_backslashes(self, controller_instance):
        """测试反斜杠路径处理"""
        # Arrange
        test_path = "folder\\test_video.mp4"
        
        # Act
        result = controller_instance.fix_file_path(test_path)
        
        # Assert
        assert result is not None
        assert "\\" not in result or "/" in result  # 应该标准化路径分隔符
    
    def test_get_video_files_empty_directory(self, controller_instance, temp_video_dir):
        """测试空目录的视频文件获取"""
        # Arrange
        controller_instance.video_dir = temp_video_dir
        
        # Act
        result = controller_instance.get_video_files()
        
        # Assert
        assert isinstance(result, dict)
        assert result['code'] == 0
        assert isinstance(result['files'], list)
        assert len(result['files']) == 0
    
    def test_get_video_files_with_videos(self, controller_instance, temp_video_dir):
        """测试包含视频文件的目录"""
        # Arrange
        controller_instance.video_dir = temp_video_dir
        video_files = ['test1.mp4', 'test2.avi', 'test3.mkv', 'not_video.txt']
        
        for filename in video_files:
            filepath = os.path.join(temp_video_dir, filename)
            with open(filepath, 'w') as f:
                f.write("test content")
        
        # Act
        result = controller_instance.get_video_files()
        
        # Assert
        assert result['code'] == 0
        assert len(result['files']) == 3  # 只有视频文件
        video_names = [f['name'] for f in result['files']]
        assert 'test1.mp4' in video_names
        assert 'test2.avi' in video_names
        assert 'test3.mkv' in video_names
        assert 'not_video.txt' not in video_names
    
    def test_get_video_files_nonexistent_directory(self, controller_instance):
        """测试不存在的目录"""
        # Arrange
        controller_instance.video_dir = "/nonexistent/directory"
        
        # Act
        result = controller_instance.get_video_files()
        
        # Assert
        assert result['code'] == -1
        assert 'msg' in result
        assert '目录不存在' in result['msg'] or 'not exist' in result['msg'].lower()
    
    def test_validate_file_path_security(self, controller_instance, temp_video_dir):
        """测试文件路径安全验证"""
        # Arrange
        controller_instance.video_dir = temp_video_dir
        
        # Test cases for path traversal attacks
        dangerous_paths = [
            "../../../etc/passwd",
            "..\\..\\windows\\system32\\config\\sam",
            "/etc/passwd",
            "C:\\Windows\\System32\\config\\SAM",
            "test/../../../sensitive_file.txt"
        ]
        
        for dangerous_path in dangerous_paths:
            # Act & Assert
            with pytest.raises((ValueError, SecurityError, OSError)) or \
                 controller_instance.fix_file_path(dangerous_path) is None:
                # 应该抛出异常或返回None
                pass
    
    def test_file_exists_check(self, controller_instance, sample_video_file):
        """测试文件存在性检查"""
        # Arrange
        controller_instance.video_dir = os.path.dirname(sample_video_file)
        filename = os.path.basename(sample_video_file)
        
        # Act
        fixed_path = controller_instance.fix_file_path(filename)
        
        # Assert
        assert fixed_path is not None
        assert os.path.exists(fixed_path)
    
    def test_file_size_calculation(self, controller_instance, sample_video_file):
        """测试文件大小计算"""
        # Arrange
        controller_instance.video_dir = os.path.dirname(sample_video_file)
        
        # Act
        result = controller_instance.get_video_files()
        
        # Assert
        assert result['code'] == 0
        assert len(result['files']) > 0
        
        file_info = result['files'][0]
        assert 'size' in file_info
        assert isinstance(file_info['size'], (int, float))
        assert file_info['size'] >= 0
    
    def test_supported_video_formats(self, controller_instance, temp_video_dir):
        """测试支持的视频格式"""
        # Arrange
        controller_instance.video_dir = temp_video_dir
        
        supported_formats = [
            'test.mp4', 'test.avi', 'test.mkv', 'test.mov', 
            'test.wmv', 'test.flv', 'test.webm', 'test.m4v'
        ]
        
        unsupported_formats = [
            'test.txt', 'test.doc', 'test.pdf', 'test.jpg', 'test.png'
        ]
        
        # Create test files
        all_files = supported_formats + unsupported_formats
        for filename in all_files:
            filepath = os.path.join(temp_video_dir, filename)
            with open(filepath, 'w') as f:
                f.write("test content")
        
        # Act
        result = controller_instance.get_video_files()
        
        # Assert
        assert result['code'] == 0
        returned_files = [f['name'] for f in result['files']]
        
        # 检查支持的格式都被包含
        for supported_file in supported_formats:
            assert supported_file in returned_files
        
        # 检查不支持的格式都被排除
        for unsupported_file in unsupported_formats:
            assert unsupported_file not in returned_files


class TestPathNormalization:
    """路径标准化测试"""
    
    def test_windows_path_normalization(self, controller_instance):
        """测试Windows路径标准化"""
        # Arrange
        windows_path = "folder\\subfolder\\video.mp4"
        
        # Act
        result = controller_instance.fix_file_path(windows_path)
        
        # Assert
        assert result is not None
        # 路径应该被标准化（具体实现取决于系统）
    
    def test_unix_path_normalization(self, controller_instance):
        """测试Unix路径标准化"""
        # Arrange
        unix_path = "folder/subfolder/video.mp4"
        
        # Act
        result = controller_instance.fix_file_path(unix_path)
        
        # Assert
        assert result is not None
    
    def test_mixed_path_separators(self, controller_instance):
        """测试混合路径分隔符"""
        # Arrange
        mixed_path = "folder\\subfolder/video.mp4"
        
        # Act
        result = controller_instance.fix_file_path(mixed_path)
        
        # Assert
        assert result is not None
        # 路径分隔符应该被统一
    
    def test_relative_path_resolution(self, controller_instance, temp_video_dir):
        """测试相对路径解析"""
        # Arrange
        controller_instance.video_dir = temp_video_dir
        relative_path = "./video.mp4"
        
        # Act
        result = controller_instance.fix_file_path(relative_path)
        
        # Assert
        assert result is not None
        assert os.path.isabs(result)  # 应该返回绝对路径


# 如果需要测试实际的文件操作，可以添加集成测试
class TestFileOperationsIntegration:
    """文件操作集成测试"""
    
    @pytest.mark.integration
    def test_real_video_file_detection(self, controller_instance):
        """测试真实视频文件检测（需要真实的视频文件）"""
        # 这个测试需要真实的视频文件，可以跳过或使用特定的测试文件
        pytest.skip("需要真实的视频文件进行测试")
    
    @pytest.mark.integration  
    def test_large_directory_performance(self, controller_instance):
        """测试大目录的性能"""
        # 测试包含大量文件的目录的性能
        pytest.skip("性能测试，需要大量文件")
