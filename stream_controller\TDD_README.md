# Stream Controller TDD 单元测试指南

## 🎯 测试驱动开发 (TDD) 概述

本项目采用 TDD 方式进行开发，遵循 **红-绿-重构** 循环：

1. **红色阶段**: 编写失败的测试用例
2. **绿色阶段**: 编写最少的代码使测试通过
3. **重构阶段**: 优化代码结构，保持测试通过

## 📁 测试结构

```
stream_controller/
├── tests/                          # 测试目录
│   ├── __init__.py                 # 测试包初始化
│   ├── conftest.py                 # pytest配置和共享fixtures
│   ├── test_file_operations.py     # 文件操作测试
│   ├── test_ffmpeg_operations.py   # FFmpeg操作测试
│   ├── test_api_endpoints.py       # API端点测试
│   ├── test_queue_management.py    # 队列管理测试
│   ├── test_monitoring.py          # 监控功能测试
│   ├── test_security.py           # 安全性测试
│   └── requirements.txt            # 测试依赖
├── pytest.ini                     # pytest配置文件
├── run_tests.py                    # 测试运行脚本
└── TDD_README.md                   # 本文档
```

## 🧪 测试分类

### 1. 单元测试 (Unit Tests)
- **文件**: `test_*.py`
- **标记**: `@pytest.mark.unit`
- **目的**: 测试单个函数或方法的功能
- **特点**: 快速、独立、无外部依赖

### 2. 集成测试 (Integration Tests)
- **标记**: `@pytest.mark.integration`
- **目的**: 测试组件间的交互
- **特点**: 可能需要真实的外部资源

### 3. 安全测试 (Security Tests)
- **标记**: `@pytest.mark.security`
- **目的**: 验证安全防护措施
- **特点**: 测试各种攻击场景

### 4. 性能测试 (Performance Tests)
- **标记**: `@pytest.mark.performance`
- **目的**: 验证性能指标
- **特点**: 可能运行时间较长

## 🚀 快速开始

### 1. 安装测试依赖

```bash
# 方式1: 使用测试脚本
python run_tests.py --install

# 方式2: 直接安装
pip install -r tests/requirements.txt
```

### 2. 运行测试

```bash
# 运行所有测试
python run_tests.py --all

# 运行单元测试
python run_tests.py --unit

# 运行特定测试文件
pytest tests/test_file_operations.py

# 运行特定测试方法
pytest tests/test_file_operations.py::TestFileOperations::test_fix_file_path_with_valid_path

# 并行运行测试
python run_tests.py --all --parallel

# 生成详细报告
python run_tests.py --report
```

### 3. 查看覆盖率

```bash
# 运行测试并生成覆盖率报告
pytest --cov=stream_controller --cov-report=html

# 查看HTML报告
# 打开 htmlcov/index.html
```

## 📝 编写测试的最佳实践

### 1. 测试命名规范

```python
def test_[功能]_[场景]_[期望结果](self):
    """测试描述"""
    pass

# 示例
def test_start_push_with_valid_file_should_succeed(self):
    """测试使用有效文件启动推流应该成功"""
    pass

def test_start_push_with_nonexistent_file_should_fail(self):
    """测试使用不存在文件启动推流应该失败"""
    pass
```

### 2. AAA 模式 (Arrange-Act-Assert)

```python
def test_add_to_queue_success(self, controller_instance, sample_video_file):
    # Arrange - 准备测试数据
    filepath = sample_video_file
    
    # Act - 执行被测试的操作
    result = controller_instance.add_to_queue(filepath)
    
    # Assert - 验证结果
    assert result['code'] == 0
    assert len(controller_instance.play_queue) == 1
```

### 3. 使用 Fixtures

```python
@pytest.fixture
def controller_instance(temp_video_dir):
    """创建控制器实例"""
    controller = LocalFFmpegController()
    controller.video_dir = temp_video_dir
    return controller

def test_something(controller_instance):
    # 使用fixture提供的实例
    result = controller_instance.some_method()
    assert result is not None
```

### 4. Mock 外部依赖

```python
def test_start_push_success(self, controller_instance, mock_subprocess):
    """测试成功启动推流"""
    # Arrange
    mock_popen, mock_process = mock_subprocess
    filepath = "test_video.mp4"
    
    # Act
    result = controller_instance.start_push(filepath)
    
    # Assert
    assert result['code'] == 0
    mock_popen.assert_called_once()
```

## 🔧 测试工具和配置

### 1. pytest 配置 (pytest.ini)

```ini
[tool:pytest]
testpaths = tests
markers =
    unit: 单元测试
    integration: 集成测试
    security: 安全测试
    performance: 性能测试
addopts = --cov=stream_controller --cov-report=html
```

### 2. 常用 pytest 插件

- **pytest-cov**: 代码覆盖率
- **pytest-mock**: Mock功能增强
- **pytest-xdist**: 并行测试
- **pytest-html**: HTML测试报告
- **pytest-benchmark**: 性能基准测试

### 3. Mock 策略

```python
# Mock subprocess
@pytest.fixture
def mock_subprocess():
    with patch('subprocess.Popen') as mock_popen:
        mock_process = Mock()
        mock_process.pid = 12345
        mock_popen.return_value = mock_process
        yield mock_popen, mock_process

# Mock psutil
@pytest.fixture
def mock_psutil():
    with patch('psutil.Process') as mock_process_class:
        mock_process = Mock()
        mock_process.cpu_percent.return_value = 25.5
        mock_process_class.return_value = mock_process
        yield mock_process
```

## 📊 测试报告

### 1. 覆盖率报告
- **HTML报告**: `htmlcov/index.html`
- **终端报告**: 运行测试时显示
- **目标**: 保持 80% 以上的代码覆盖率

### 2. 测试报告
- **HTML报告**: `reports/test_report.html`
- **JUnit XML**: `reports/junit.xml`
- **包含**: 测试结果、执行时间、失败详情

## 🔍 TDD 开发流程示例

### 1. 编写失败的测试

```python
def test_switch_video_seamlessly(self, controller_instance):
    """测试无缝切换视频"""
    # Arrange
    old_video = "video1.mp4"
    new_video = "video2.mp4"
    
    # 启动第一个视频
    controller_instance.start_push(old_video)
    
    # Act
    result = controller_instance.switch_video(new_video)
    
    # Assert
    assert result['code'] == 0
    assert controller_instance.current_file == new_video
    # 这个测试会失败，因为方法还没实现
```

### 2. 实现最小功能

```python
def switch_video(self, new_filepath):
    """切换视频（最小实现）"""
    # 停止当前推流
    self.stop_push()
    
    # 启动新推流
    result = self.start_push(new_filepath)
    
    return result
```

### 3. 重构和优化

```python
def switch_video(self, new_filepath, seamless=True):
    """切换视频（优化版本）"""
    if seamless:
        # 实现无缝切换逻辑
        return self._seamless_switch(new_filepath)
    else:
        # 普通切换
        self.stop_push()
        return self.start_push(new_filepath)
```

## 🎯 测试目标

### 1. 代码覆盖率
- **目标**: ≥ 80%
- **关键模块**: ≥ 90%
- **新增代码**: 100%

### 2. 测试类型分布
- **单元测试**: 70%
- **集成测试**: 20%
- **端到端测试**: 10%

### 3. 性能指标
- **测试执行时间**: < 5分钟
- **单个测试**: < 1秒
- **并行测试**: 支持

## 🚨 常见问题和解决方案

### 1. 测试运行缓慢
```bash
# 使用并行测试
pytest -n auto

# 跳过慢速测试
pytest -m "not slow"
```

### 2. Mock 不生效
```python
# 确保 patch 路径正确
# 错误: @patch('subprocess.Popen')
# 正确: @patch('stream_controller.subprocess.Popen')
```

### 3. 测试隔离问题
```python
# 使用 setUp 和 tearDown
def setup_method(self):
    """每个测试方法前执行"""
    self.controller = LocalFFmpegController()

def teardown_method(self):
    """每个测试方法后执行"""
    if self.controller.current_process:
        self.controller.stop_push()
```

## 📚 参考资源

- [pytest 官方文档](https://docs.pytest.org/)
- [Python Mock 指南](https://docs.python.org/3/library/unittest.mock.html)
- [TDD 最佳实践](https://testdriven.io/blog/modern-tdd/)
- [代码覆盖率指南](https://coverage.readthedocs.io/)

## 🤝 贡献指南

1. **新功能开发**: 先写测试，再实现功能
2. **Bug修复**: 先写重现测试，再修复
3. **代码提交**: 确保所有测试通过
4. **代码审查**: 检查测试覆盖率和质量

---

通过遵循这个TDD指南，我们可以确保 Stream Controller 项目的代码质量、可维护性和可靠性。
