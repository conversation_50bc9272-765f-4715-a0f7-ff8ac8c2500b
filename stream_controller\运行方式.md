  # 基本启动（已包含所有必要端口）
  docker run -d --name zlmediakit \
    -p 1935:1935 -p 8080:80 -p 8554:554 -p 10000:10000 -p 10000:10000/udp \
    -p 8000:8000/udp -p 30000-30500:30000-30500/udp \
    -e ZLM_API_SECRET="" \
    zlmediakit/zlmediakit:latest

  2. 检查运行状态

  docker ps
  docker logs zlmediakit

  🌐 Web 管理页面

 

  🎯 完整端口映射表

  | 协议    |   端口       |    用途       |
  |---------|-------------|---------------|
  | HTTP    | 8080        | Web管理界面    |
  | RTMP    | 1935        | RTMP推流/播放  |
  | RTSP    | 8554        | RTSP推流/播放  |
  | WebRTC  | 8000        | WebRTC信令     |
  | RTP/UDP | 10000       | RTP推流        |
  | 媒体端口 | 30000-30500 | WebRTC媒体传输 |


  🔍 正确URL格式

  Web管理页面

  http://localhost:8080/               # 主页面
  http://localhost:8080/index/api/getMediaList
  http://localhost:8080/webrtc/        # WebRTC测试页面

  WebRTC播放

  WebSocket: ws://localhost:8000
  WebRTC页面: http://localhost:8080/webrtc/

  推流地址

  RTMP推流: rtmp://localhost:1935/live/stream1

  🐳 Docker启动命令（已修正）

  docker run -d --name zlmediakit \
    -p 8080:80 \        # Web管理界面
    -p 1935:1935 \      # RTMP
    -p 8554:554 \       # RTSP
    -p 8000:8000/udp \  # WebRTC信令
    -p 10000:10000/udp \ # RTP
    -p 30000-30500:30000-30500/udp \ # WebRTC媒体
    -e ZLM_API_SECRET="" \
    zlmediakit/zlmediakit:latest

  ✅ 验证URL

  - Web管理: http://localhost:8080
  - WebRTC: http://localhost:8080/webrtc/
  - API测试: http://localhost:8080/index/api/getMediaList



  
  4. Windows防火墙设置

  # 添加防火墙规则（管理员权限）
  netsh advfirewall firewall add rule name="ZLMediaKit-HTTP" dir=in action=allow protocol=TCP localport=80
  netsh advfirewall firewall add rule name="ZLMediaKit-RTMP" dir=in action=allow protocol=TCP localport=1935
  netsh advfirewall firewall add rule name="ZLMediaKit-WEBRTC" dir=in action=allow protocol=TCP localport=8000

