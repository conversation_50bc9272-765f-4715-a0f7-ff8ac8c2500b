﻿/*
 * Copyright (c) 2016-present The ZLMediaKit project authors. All Rights Reserved.
 *
 * This file is part of ZLMediaKit(https://github.com/ZLMediaKit/ZLMediaKit).
 *
 * Use of this source code is governed by MIT-like license that can be found in the
 * LICENSE file in the root of the source tree. All contributing project authors
 * may be found in the AUTHORS file in the root of the source tree.
 */

#ifndef MK_PUSHER_H
#define MK_PUSHER_H

#include "mk_common.h"
#include "mk_events_objects.h"

#ifdef __cplusplus
extern "C" {
#endif

typedef struct mk_pusher_t *mk_pusher;

/**
 * 推流结果或推流中断事件的回调
 * @param user_data 用户数据指针
 * @param err_code 错误代码，0为成功
 * @param err_msg 错误提示
 * Callback for streaming result or streaming interruption event
 * @param user_data User data pointer
 * @param err_code Error code, 0 for success
 * @param err_msg Error message
 
 * [AUTO-TRANSLATED:6e7d5c79]
 */
typedef void(API_CALL *on_mk_push_event)(void *user_data,int err_code,const char *err_msg);

/**
 * 绑定的MediaSource对象并创建rtmp[s]/rtsp[s]推流器
 * MediaSource通过mk_media_create或mk_proxy_player_create或推流生成
 * 该MediaSource对象必须已注册
 *
 * @param schema 绑定的MediaSource对象所属协议，支持rtsp/rtmp
 * @param vhost 绑定的MediaSource对象的虚拟主机，一般为__defaultVhost__
 * @param app 绑定的MediaSource对象的应用名，一般为live
 * @param stream 绑定的MediaSource对象的流id
 * @return 对象指针
 * Bind the MediaSource object and create an rtmp[s]/rtsp[s] pusher
 * MediaSource is generated by mk_media_create or mk_proxy_player_create or streaming
 * The MediaSource object must be registered
 *
 * @param schema Protocol to which the bound MediaSource object belongs, supporting rtsp/rtmp
 * @param vhost Virtual host of the bound MediaSource object, generally __defaultVhost__
 * @param app Application name of the bound MediaSource object, generally live
 * @param stream Stream id of the bound MediaSource object
 * @return Object pointer
 
 * [AUTO-TRANSLATED:9366fdbc]
 */
API_EXPORT mk_pusher API_CALL mk_pusher_create(const char *schema,const char *vhost,const char *app, const char *stream);

/**
 * 绑定的MediaSource对象并创建rtmp[s]/rtsp[s]推流器
 * MediaSource通过mk_media_create或mk_proxy_player_create或推流生成
 * 该MediaSource对象必须已注册
 *
 * @param src MediaSource对象
 * @return 对象指针
 * Bind the MediaSource object and create an rtmp[s]/rtsp[s] pusher
 * MediaSource is generated by mk_media_create or mk_proxy_player_create or streaming
 * The MediaSource object must be registered
 *
 * @param src MediaSource object
 * @return Object pointer
 
 * [AUTO-TRANSLATED:34ca024a]
 */
API_EXPORT mk_pusher API_CALL mk_pusher_create_src(mk_media_source src);

/**
 * 释放推流器
 * @param ctx 推流器指针
 * Release the pusher
 * @param ctx Pusher pointer
 
 * [AUTO-TRANSLATED:55fd6b8b]
 */
API_EXPORT void API_CALL mk_pusher_release(mk_pusher ctx);

/**
 * 设置推流器配置选项
 * @param ctx 推流器指针
 * @param key 配置项键,支持 net_adapter/rtp_type/rtsp_user/rtsp_pwd/protocol_timeout_ms/media_timeout_ms/beat_interval_ms
 * @param val 配置项值,如果是整形，需要转换成统一转换成string
 * Set the pusher configuration options
 * @param ctx Pusher pointer
 * @param key Configuration item key, supports net_adapter/rtp_type/rtsp_user/rtsp_pwd/protocol_timeout_ms/media_timeout_ms/beat_interval_ms
 * @param val Configuration item value, if it is an integer, it needs to be converted to a unified string
 
 * [AUTO-TRANSLATED:0e3ce06d]
 */
API_EXPORT void API_CALL mk_pusher_set_option(mk_pusher ctx, const char *key, const char *val);

/**
 * 开始推流
 * @param ctx 推流器指针
 * @param url 推流地址，支持rtsp[s]/rtmp[s]
 * Start streaming
 * @param ctx Pusher pointer
 * @param url Streaming address, supports rtsp[s]/rtmp[s]
 
 * [AUTO-TRANSLATED:45c0a836]
 */
API_EXPORT void API_CALL mk_pusher_publish(mk_pusher ctx,const char *url);

/**
 * 设置推流器推流结果回调函数
 * @param ctx 推流器指针
 * @param cb 回调函数指针,不得为null
 * @param user_data 用户数据指针
 * Set the pusher streaming result callback function
 * @param ctx Pusher pointer
 * @param cb Callback function pointer, must not be null
 * @param user_data User data pointer
 
 * [AUTO-TRANSLATED:a45fb6e4]
 */
API_EXPORT void API_CALL mk_pusher_set_on_result(mk_pusher ctx, on_mk_push_event cb, void *user_data);
API_EXPORT void API_CALL mk_pusher_set_on_result2(mk_pusher ctx, on_mk_push_event cb, void *user_data, on_user_data_free user_data_free);

/**
 * 设置推流被异常中断的回调
 * @param ctx 推流器指针
 * @param cb 回调函数指针,不得为null
 * @param user_data 用户数据指针
 * Set the callback for the streaming being abnormally interrupted
 * @param ctx Pusher pointer
 * @param cb Callback function pointer, must not be null
 * @param user_data User data pointer
 
 * [AUTO-TRANSLATED:9e11a215]
 */
API_EXPORT void API_CALL mk_pusher_set_on_shutdown(mk_pusher ctx, on_mk_push_event cb, void *user_data);
API_EXPORT void API_CALL mk_pusher_set_on_shutdown2(mk_pusher ctx, on_mk_push_event cb, void *user_data, on_user_data_free user_data_free);

#ifdef __cplusplus
}
#endif
#endif //MK_PUSHER_H
