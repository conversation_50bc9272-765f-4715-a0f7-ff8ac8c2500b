@echo off
chcp 65001 >nul
title FFmpeg推流控制器

echo.
echo ========================================
echo    🎬 FFmpeg推流控制器启动脚本
echo ========================================
echo.

cd /d "%~dp0"

echo 正在检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python未安装或未添加到PATH
    echo 请先安装Python并添加到系统PATH
    pause
    exit /b 1
)

echo ✅ Python环境检查通过
echo.

echo 正在启动服务器...
echo 如需调试模式，请使用: python start_server.py --debug
echo.

python start_server.py

echo.
echo 服务器已停止，按任意键退出...
pause >nul 