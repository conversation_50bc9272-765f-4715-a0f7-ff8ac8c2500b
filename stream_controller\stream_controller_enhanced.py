#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
本地FFmpeg推流控制器
支持Web界面控制、无缝切换、多文件队列
"""

import os
import subprocess
import json
import time
import threading
import re
from collections import deque
from datetime import datetime
from flask import Flask, request, jsonify, render_template_string
from flask_socketio import SocketIO, emit
import psutil

class LocalFFmpegController:
    def __init__(self):
        self.video_dir = r"D:\Dev\ZLMediaKit\ZLMediaKit\Video-files"
        self.rtmp_url = "rtmp://127.0.0.1:1935/live/stream1"
        self.backup_rtmp_url = "rtmp://127.0.0.1:1935/live/stream2"  # 备用推流地址
        self.zlmedia_secret = "035c73f7-bb6b-4889-a715-d9eb2d1925cc"  # ZLMediaKit API密钥
        self.zlmedia_api_url = "http://127.0.0.1:8080/index/api"     # ZLMediaKit API地址
        self.current_process = None
        self.backup_process = None  # 备用推流进程
        self.current_file = None
        self.backup_file = None  # 备用文件
        self.play_queue = []
        self.is_playing = False
        self.queue_thread = None
        
        # 监控相关属性
        self.monitor_thread = None
        self.is_monitoring = False
        self.ffmpeg_logs = deque(maxlen=1000)  # 保存最近1000条日志
        self.performance_data = deque(maxlen=100)  # 保存最近100个性能数据点
        self.start_time = None
        self.frame_count = 0
        self.bitrate_history = deque(maxlen=50)
    
    def fix_file_path(self, filepath):
        """修复文件路径格式问题"""
        if not filepath:
            return None
            
        print(f"原始路径: {repr(filepath)}")
        
        # 处理路径中可能的编码问题
        if isinstance(filepath, bytes):
            filepath = filepath.decode('utf-8', errors='ignore')
        
        # 修复可能的路径分隔符问题
        # 从错误信息看，路径变成了 "D:DevZLMediaKitZLMediaKitVideo-files	est_video1.mp4"
        # 可能是反斜杠被转义或丢失了
        
        # 尝试修复缺失的路径分隔符
        if 'D:Dev' in filepath and 'ZLMediaKit' in filepath:
            # 这是我们预期的路径格式，需要修复分隔符
            fixed_path = filepath.replace('D:Dev', 'D:\\Dev')
            fixed_path = fixed_path.replace('ZLMediaKitZLMediaKit', 'ZLMediaKit\\ZLMediaKit')
            fixed_path = fixed_path.replace('Video-files\t', 'Video-files\\')
            fixed_path = fixed_path.replace('\t', '\\')
            print(f"修复后路径: {repr(fixed_path)}")
            filepath = fixed_path
        
        # 标准化路径
        filepath = os.path.normpath(filepath)
        print(f"标准化路径: {repr(filepath)}")
        
        return filepath
        
    def get_video_files(self):
        """获取可用视频文件"""
        files = []
        print(f"扫描视频目录: {self.video_dir}")
        
        for ext in ['*.mp4', '*.avi', '*.mkv', '*.mov']:
            import glob
            pattern = os.path.join(self.video_dir, ext)
            found_files = glob.glob(pattern)
            print(f"模式 {pattern} 找到文件: {found_files}")
            files.extend(found_files)
        
        result = [{
            'name': os.path.basename(f),
            'path': f,
            'size': os.path.getsize(f)
        } for f in files]
        
        print(f"返回文件列表: {result}")
        return result
    
    def analyze_video_and_warmup(self, filepath):
        """
        分析视频内容并预热编码器
        """
        try:
            # 使用ffprobe分析视频信息
            probe_cmd = [
                'ffprobe', '-v', 'quiet', '-print_format', 'json',
                '-show_format', '-show_streams', filepath
            ]
            
            result = subprocess.run(
                probe_cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0:
                probe_data = json.loads(result.stdout)
                
                # 提取视频信息
                video_stream = None
                audio_stream = None
                for stream in probe_data.get('streams', []):
                    if stream.get('codec_type') == 'video':
                        video_stream = stream
                    elif stream.get('codec_type') == 'audio':
                        audio_stream = stream
                
                video_info = {}
                if video_stream:
                    # 提取视频信息
                    video_info.update({
                        'width': int(video_stream.get('width', 1920)),
                        'height': int(video_stream.get('height', 1080)),
                        'codec_name': video_stream.get('codec_name', 'h264'),
                        'avg_frame_rate': video_stream.get('avg_frame_rate', '30/1')
                    })
                
                if audio_stream:
                    # 提取音频信息
                    video_info.update({
                        'audio_codec': audio_stream.get('codec_name', 'aac'),
                        'sample_rate': int(audio_stream.get('sample_rate', 44100)),
                        'channels': int(audio_stream.get('channels', 2))
                    })
                
                # 根据视频信息决定编码预设
                if video_info.get('width', 1920) > 1920 or video_info.get('height', 1080) > 1080:
                    video_info['preset'] = 'faster'  # 高分辨率使用更快的预设
                else:
                    video_info['preset'] = 'veryfast'  # 普通分辨率使用极快预设
                
                print(f"视频分析完成: {video_info}")
                return video_info
        except subprocess.TimeoutExpired:
            print("视频分析超时")
        except Exception as e:
            print(f"视频分析失败: {e}")
        
        # 默认配置
        return {
            'width': 1920,
            'height': 1080,
            'preset': 'veryfast',
            'avg_frame_rate': '30/1'
        }
    
    def start_push(self, filepath, rtmp_url=None, video_info=None):
        """开始推流单个文件"""
        if not rtmp_url:
            rtmp_url = self.rtmp_url
            
        # 检查是否已有相同RTMP URL的推流在运行
        if rtmp_url == self.rtmp_url and self.current_process and self.current_process.poll() is None:
            return {'code': -1, 'msg': '主推流已在运行'}
        elif rtmp_url == self.backup_rtmp_url and self.backup_process and self.backup_process.poll() is None:
            return {'code': -1, 'msg': '备用推流已在运行'}
        
        # 修复文件路径
        filepath = self.fix_file_path(filepath)
        
        if not os.path.exists(filepath):
            # 尝试修复路径问题
            if not os.path.isabs(filepath):
                # 如果是相对路径，与视频目录拼接
                full_path = os.path.join(self.video_dir, filepath)
                full_path = os.path.normpath(full_path)
                print(f"尝试完整路径: {full_path}")
                if os.path.exists(full_path):
                    filepath = full_path
                else:
                    return {'code': -2, 'msg': f'文件不存在: {filepath}'}
            else:
                return {'code': -2, 'msg': f'文件不存在: {filepath}'}
        
        try:
            # 如果没有提供视频信息，则分析视频
            if video_info is None:
                video_info = self.analyze_video_and_warmup(filepath)
            
            # 构建FFmpeg命令，添加同步和优化参数
            cmd = [
                'ffmpeg', '-re', '-stream_loop', '-1',
                '-i', filepath,
                '-copyts',  # 保持时间戳
                '-vsync', 'cfr',  # 恒定帧率
                '-muxdelay', '0',  # 减少复用器延迟
                '-flvflags', 'no_duration_filesize',  # 优化FLV封装
                '-c:v', 'libx264',
                '-preset', video_info.get('preset', 'veryfast'),
                '-tune', 'zerolatency',
                '-c:a', 'aac', '-ar', str(video_info.get('sample_rate', 44100)), 
                '-b:a', '128k',
                '-pix_fmt', 'yuv420p', '-g', '50',
                '-f', 'flv', rtmp_url
            ]
            
            print(f"启动推流，命令: {' '.join(cmd)}")
            
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                creationflags=subprocess.CREATE_NEW_PROCESS_GROUP
            )
            
            # 根据RTMP URL设置对应的进程和文件
            if rtmp_url == self.rtmp_url:
                self.current_process = process
                self.current_file = filepath
            else:
                self.backup_process = process
                self.backup_file = filepath
                
            # 如果是主推流，启动监控
            if rtmp_url == self.rtmp_url:
                self.start_time = time.time()
                self.frame_count = 0
                self.start_monitoring()
            
            return {'code': 0, 'msg': '推流开始', 'file': os.path.basename(filepath)}
        except Exception as e:
            return {'code': -3, 'msg': str(e)}
    
    def stop_push(self, target="main"):
        """停止推流
        target: "main" - 停止主推流, "backup" - 停止备用推流, "all" - 停止所有推流
        """
        stopped = []
        
        if target in ["main", "all"]:
            if self.current_process and self.current_process.poll() is None:
                try:
                    # 只有停止主推流时才停止监控
                    if target == "main":
                        self.stop_monitoring()
                    
                    self.current_process.terminate()
                    self.current_process.wait(timeout=5)
                    self.current_process = None
                    self.current_file = None
                    if target == "main":
                        self.start_time = None
                    stopped.append("主推流")
                except Exception as e:
                    return {'code': -1, 'msg': f'停止主推流失败: {str(e)}'}
        
        if target in ["backup", "all"]:
            if self.backup_process and self.backup_process.poll() is None:
                try:
                    self.backup_process.terminate()
                    self.backup_process.wait(timeout=5)
                    self.backup_process = None
                    self.backup_file = None
                    stopped.append("备用推流")
                except Exception as e:
                    return {'code': -1, 'msg': f'停止备用推流失败: {str(e)}'}
        
        if stopped:
            return {'code': 0, 'msg': f'已停止: {", ".join(stopped)}'}
        return {'code': 0, 'msg': '无运行中的推流'}
    
    def switch_zlmedia_source(self, main_stream, backup_stream):
        """
        调用ZLMediaKit API切换主备源
        """
        try:
            import requests
            
            # 设置主备源配置
            config_data = {
                "secret": self.zlmedia_secret,
                "main_stream": main_stream,
                "backup_stream": backup_stream
            }
            
            response = requests.post(
                f"{self.zlmedia_api_url}/setServerConfig",
                json=config_data,
                timeout=5
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get("code", -1) == 0:
                    print("ZLMediaKit主备源配置成功")
                    return True
                else:
                    print(f"ZLMediaKit配置失败: {result.get('msg', '未知错误')}")
            else:
                print(f"ZLMediaKit API请求失败: {response.status_code}")
        except ImportError:
            print("未安装requests库，跳过ZLMediaKit API调用")
        except Exception as e:
            print(f"ZLMediaKit API调用失败: {e}")
        
        return False
    
    def switch_video(self, new_filepath):
        """无缝切换视频 - 改进版实现"""
        # 修复文件路径
        new_filepath = self.fix_file_path(new_filepath)
        
        if not os.path.exists(new_filepath):
            # 尝试修复路径问题
            if not os.path.isabs(new_filepath):
                # 如果是相对路径，与视频目录拼接
                full_path = os.path.join(self.video_dir, new_filepath)
                full_path = os.path.normpath(full_path)
                print(f"尝试完整路径: {full_path}")
                if os.path.exists(full_path):
                    new_filepath = full_path
                else:
                    return {'code': -1, 'msg': f'文件不存在: {new_filepath}'}
            else:
                return {'code': -1, 'msg': f'文件不存在: {new_filepath}'}
        
        try:
            # 1. 分析视频内容并预热编码器
            video_info = self.analyze_video_and_warmup(new_filepath)
            
            # 2. 首先启动备用推流（推送到备用地址）
            backup_result = self.start_push(new_filepath, self.backup_rtmp_url, video_info)
            if backup_result['code'] != 0:
                return {'code': -2, 'msg': f'启动备用推流失败: {backup_result["msg"]}'}
            
            # 3. 等待备用推流稳定（很短的时间，比如0.1秒）
            time.sleep(0.1)
            
            # 4. 调用ZLMediaKit API切换主备源（如果API调用失败，则使用原有方式）
            if self.switch_zlmedia_source(self.backup_rtmp_url, self.rtmp_url):
                # API调用成功，ZLMediaKit已切换主备源
                print("通过ZLMediaKit API完成主备源切换")
                
                # 5. 停止之前的主推流
                stop_result = self.stop_push("main")
                if stop_result['code'] != 0:
                    print(f"停止主推流警告: {stop_result['msg']}")
                
                # 6. 将备用推流提升为主推流
                self.current_process = self.backup_process
                self.current_file = self.backup_file
                self.backup_process = None
                self.backup_file = None
                
                return {'code': 0, 'msg': '通过ZLMediaKit API实现无缝切换完成', 'file': os.path.basename(new_filepath)}
            else:
                # API调用失败，回退到原有的切换方式
                print("ZLMediaKit API调用失败，使用原有切换方式")
                
                # 停止当前主推流
                stop_result = self.stop_push("main")
                if stop_result['code'] != 0:
                    # 如果停止主推流失败，也要停止备用推流
                    self.stop_push("backup")
                    return stop_result
                
                # 将备用推流提升为主推流
                self.current_process = self.backup_process
                self.current_file = self.backup_file
                self.backup_process = None
                self.backup_file = None
                
                return {'code': 0, 'msg': '本地无缝切换完成', 'file': os.path.basename(new_filepath)}
        except Exception as e:
            # 出现异常时清理所有推流
            self.stop_push("all")
            return {'code': -3, 'msg': f'切换失败: {str(e)}'}
    
    def get_status(self):
        """获取当前状态"""
        main_status = None
        backup_status = None
        
        if self.current_process and self.current_process.poll() is None:
            main_status = {
                'is_running': True,
                'current_file': self.current_file,
                'pid': self.current_process.pid
            }
        
        if self.backup_process and self.backup_process.poll() is None:
            backup_status = {
                'is_running': True,
                'current_file': self.backup_file,
                'pid': self.backup_process.pid
            }
        
        return {
            'code': 0,
            'main': main_status,
            'backup': backup_status
        }
    
    def kill_all_ffmpeg(self):
        """强制停止所有ffmpeg进程"""
        try:
            for proc in psutil.process_iter(['pid', 'name']):
                if proc.info['name'] and 'ffmpeg' in proc.info['name'].lower():
                    proc.kill()
            # 清理内部状态
            self.current_process = None
            self.backup_process = None
            self.current_file = None
            self.backup_file = None
            self.start_time = None
            return {'code': 0, 'msg': '所有ffmpeg进程已终止'}
        except Exception as e:
            return {'code': -1, 'msg': str(e)}
    
    def play_queue(self, files):
        """播放队列"""
        if self.is_playing:
            return {'code': -1, 'msg': '队列已在播放中'}
        
        self.play_queue = files
        self.is_playing = True
        
        def play_worker():
            for filepath in self.play_queue:
                if not self.is_playing:
                    break
                
                print(f"正在播放: {os.path.basename(filepath)}")
                result = self.switch_video(filepath)
                if result['code'] != 0:
                    print(f"播放失败: {result['msg']}")
                    break
                
                time.sleep(30)  # 播放30秒后切换下一个
            
            self.is_playing = False
        
        self.queue_thread = threading.Thread(target=play_worker)
        self.queue_thread.start()
        
        return {'code': 0, 'msg': '队列播放开始'}
    
    def start_monitoring(self):
        """开始监控FFmpeg进程"""
        if self.is_monitoring:
            return
        
        self.is_monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_worker)
        self.monitor_thread.daemon = True
        self.monitor_thread.start()
    
    def stop_monitoring(self):
        """停止监控"""
        self.is_monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=1)
    
    def _monitor_worker(self):
        """监控工作线程"""
        while self.is_monitoring:
            try:
                if self.current_process and self.current_process.poll() is None:
                    # 获取进程性能数据
                    process = psutil.Process(self.current_process.pid)
                    cpu_percent = process.cpu_percent()
                    memory_info = process.memory_info()
                    memory_mb = memory_info.rss / 1024 / 1024
                    
                    # 读取FFmpeg输出
                    self._read_ffmpeg_output()
                    
                    # 保存性能数据
                    perf_data = {
                        'timestamp': datetime.now().isoformat(),
                        'cpu_percent': cpu_percent,
                        'memory_mb': memory_mb,
                        'frame_count': self.frame_count,
                        'uptime': time.time() - self.start_time if self.start_time else 0
                    }
                    self.performance_data.append(perf_data)
                    
                    # 通过WebSocket发送实时数据
                    if hasattr(self, 'socketio'):
                        self.socketio.emit('monitor_update', perf_data)
                
                time.sleep(1)  # 每秒更新一次
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                # 进程已结束或无权限访问
                break
            except Exception as e:
                print(f"监控错误: {e}")
                break
    
    def _read_ffmpeg_output(self):
        """读取FFmpeg输出并解析"""
        if not self.current_process:
            return
        
        try:
            # 非阻塞读取stderr（FFmpeg输出到stderr）
            import select
            if hasattr(select, 'select'):
                ready, _, _ = select.select([self.current_process.stderr], [], [], 0)
                if ready:
                    line = self.current_process.stderr.readline()
                    if line:
                        self._parse_ffmpeg_log(line.decode('utf-8', errors='ignore'))
        except:
            # Windows下select不支持管道，使用其他方法
            pass
    
    def _parse_ffmpeg_log(self, line):
        """解析FFmpeg日志行"""
        timestamp = datetime.now().isoformat()
        log_entry = {
            'timestamp': timestamp,
            'content': line.strip(),
            'type': 'info'
        }
        
        # 解析帧数
        frame_match = re.search(r'frame=\s*(\d+)', line)
        if frame_match:
            self.frame_count = int(frame_match.group(1))
        
        # 解析比特率
        bitrate_match = re.search(r'bitrate=\s*([\d.]+)kbits/s', line)
        if bitrate_match:
            bitrate = float(bitrate_match.group(1))
            self.bitrate_history.append({
                'timestamp': timestamp,
                'bitrate': bitrate
            })
        
        # 检测错误
        if 'error' in line.lower() or 'failed' in line.lower():
            log_entry['type'] = 'error'
        elif 'warning' in line.lower():
            log_entry['type'] = 'warning'
        
        self.ffmpeg_logs.append(log_entry)
    
    def get_monitor_data(self):
        """获取监控数据"""
        current_status = self.get_status()
        
        # 获取最新性能数据
        latest_perf = list(self.performance_data)[-20:] if self.performance_data else []
        
        # 获取最新日志
        latest_logs = list(self.ffmpeg_logs)[-50:] if self.ffmpeg_logs else []
        
        # 获取比特率历史
        latest_bitrates = list(self.bitrate_history)[-20:] if self.bitrate_history else []
        
        return {
            'status': current_status,
            'performance': latest_perf,
            'logs': latest_logs,
            'bitrates': latest_bitrates,
            'summary': {
                'total_logs': len(self.ffmpeg_logs),
                'error_count': sum(1 for log in self.ffmpeg_logs if log.get('type') == 'error'),
                'warning_count': sum(1 for log in self.ffmpeg_logs if log.get('type') == 'warning'),
                'current_frame': self.frame_count,
                'uptime': time.time() - self.start_time if self.start_time else 0
            }
        }
    
    def clear_monitor_data(self):
        """清空监控数据"""
        self.ffmpeg_logs.clear()
        self.performance_data.clear()
        self.bitrate_history.clear()
        self.frame_count = 0
        return {'code': 0, 'msg': '监控数据已清空'}

# Flask Web应用
app = Flask(__name__)
app.config['SECRET_KEY'] = 'ffmpeg_monitor_secret_key'
socketio = SocketIO(app, cors_allowed_origins="*")

# 创建控制器实例
controller = LocalFFmpegController()

# 注册控制器到自身，用于WebSocket回调
controller.socketio = socketio

# 设置路由
@app.route('/')
def index():
    """主页面"""
    import os
    index_file = os.path.join(os.path.dirname(__file__), 'advanced_local_manager.html')
    if os.path.exists(index_file):
        with open(index_file, 'r', encoding='utf-8') as f:
            return f.read()
    else:
        return "页面文件未找到", 404

@app.route('/api/files')
def api_files():
    return jsonify(controller.get_video_files())

@app.route('/api/start', methods=['POST'])
def api_start():
    filepath = request.json.get('path')
    return jsonify(controller.start_push(filepath))

@app.route('/api/stop', methods=['POST'])
def api_stop():
    target = request.json.get('target', 'main')
    return jsonify(controller.stop_push(target))

@app.route('/api/switch', methods=['POST'])
def api_switch():
    filepath = request.json.get('path')
    return jsonify(controller.switch_video(filepath))

@app.route('/api/status')
def api_status():
    return jsonify(controller.get_status())

@app.route('/api/kill-all', methods=['POST'])
def api_kill_all():
    return jsonify(controller.kill_all_ffmpeg())

@app.route('/api/queue/play', methods=['POST'])
def api_queue_play():
    files = request.json.get('files', [])
    return jsonify(controller.play_queue(files))

@app.route('/api/queue/stop', methods=['POST'])
def api_queue_stop():
    controller.is_playing = False
    return jsonify({'code': 0, 'msg': '队列播放已停止'})

# 监控相关API
@app.route('/api/monitor/data')
def api_monitor_data():
    return jsonify(controller.get_monitor_data())

@app.route('/api/monitor/clear', methods=['POST'])
def api_monitor_clear():
    return jsonify(controller.clear_monitor_data())

@app.route('/api/monitor/start', methods=['POST'])
def api_monitor_start():
    controller.start_monitoring()
    return jsonify({'code': 0, 'msg': '监控已启动'})

@app.route('/api/monitor/stop', methods=['POST'])
def api_monitor_stop():
    controller.stop_monitoring()
    return jsonify({'code': 0, 'msg': '监控已停止'})

@app.route('/monitor')
def monitor_page():
    return render_template_string(MONITOR_HTML_TEMPLATE)

@app.route('/test')
def test_webrtc_page():
    """WebRTC播放器测试页面"""
    import os
    test_file = os.path.join(os.path.dirname(__file__), 'test_webrtc.html')
    with open(test_file, 'r', encoding='utf-8') as f:
        return f.read()

@app.route('/static/<path:filename>')
def static_files(filename):
    """提供静态文件服务"""
    import os
    from flask import send_from_directory
    static_dir = os.path.join(os.path.dirname(__file__), '.')
    return send_from_directory(static_dir, filename)

# WebRTC相关API
@app.route('/api/webrtc/play', methods=['POST'])
def api_webrtc_play():
    """获取WebRTC播放配置"""
    stream_id = request.json.get('stream', 'stream1')
    
    # ZLMediaKit WebRTC播放配置
    webrtc_config = {
        'api_url': 'http://127.0.0.1:8080/index/api/webrtc',
        'stream_url': f'http://127.0.0.1:8080/live/{stream_id}/hls.m3u8',
        'webrtc_url': f'webrtc://127.0.0.1:8080/live/{stream_id}',
        'flv_url': f'http://127.0.0.1:8080/live/{stream_id}.live.flv',
        'stream_id': stream_id,
        'app': 'live',
        'vhost': '__defaultVhost__'
    }
    
    return jsonify({
        'code': 0,
        'data': webrtc_config,
        'msg': 'WebRTC配置获取成功'
    })

@app.route('/api/stream/info')
def api_stream_info():
    """获取流信息"""
    try:
        import requests
        # 调用ZLMediaKit API获取流信息
        response = requests.get('http://127.0.0.1:8080/index/api/getMediaList', timeout=5)
        if response.status_code == 200:
            return jsonify(response.json())
        else:
            return jsonify({'code': -1, 'msg': '无法获取流信息'})
    except Exception as e:
        return jsonify({'code': -1, 'msg': f'获取流信息失败: {str(e)}'})

@app.route('/api/player/stats', methods=['POST'])
def api_player_stats():
    """获取播放器统计信息"""
    stats = request.json
    # 可以在这里处理播放器统计信息
    print(f"播放器统计: {stats}")
    return jsonify({'code': 0, 'msg': '统计信息已接收'})

# WebSocket事件处理
@socketio.on('connect')
def handle_connect():
    print('客户端已连接')
    emit('connection_status', {'status': 'connected'})

@socketio.on('disconnect')
def handle_disconnect():
    print('客户端已断开连接')

@socketio.on('request_monitor_data')
def handle_monitor_data_request():
    """处理监控数据请求"""
    data = controller.get_monitor_data()
    emit('monitor_data', data)

# 监控页面HTML模板
MONITOR_HTML_TEMPLATE = '''
<!-- FFmpeg监控面板 -->
<!DOCTYPE html>
<html>
<head>
    <title>FFmpeg推流监控面板</title>
    <meta charset="utf-8">
    <script src="https://cdn.socket.io/4.0.0/socket.io.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 1200px; margin: 0 auto; }
        .dashboard { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .card { border: 1px solid #ddd; border-radius: 8px; padding: 15px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .chart-container { height: 200px; margin-top: 10px; }
        .status-indicator { display: inline-block; width: 12px; height: 12px; border-radius: 50%; margin-right: 5px; }
        .status-running { background-color: #4CAF50; }
        .status-stopped { background-color: #f44336; }
        .logs { height: 200px; overflow-y: auto; background: #f5f5f5; padding: 10px; font-family: monospace; font-size: 12px; }
        .log-error { color: #f44336; }
        .log-warning { color: #ff9800; }
        .controls { margin: 20px 0; }
        button { padding: 10px 15px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; }
        .btn-primary { background-color: #2196F3; color: white; }
        .btn-danger { background-color: #f44336; color: white; }
        .btn-success { background-color: #4CAF50; color: white; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎬 FFmpeg推流监控面板</h1>
        
        <div class="controls">
            <button class="btn-primary" onclick="startMonitor()">开始监控</button>
            <button class="btn-danger" onclick="stopMonitor()">停止监控</button>
            <button class="btn-success" onclick="clearData()">清空数据</button>
        </div>
        
        <div class="dashboard">
            <div class="card">
                <h3>📊 推流状态</h3>
                <div id="streamStatus">加载中...</div>
            </div>
            
            <div class="card">
                <h3>📈 CPU使用率</h3>
                <div class="chart-container">
                    <canvas id="cpuChart"></canvas>
                </div>
            </div>
            
            <div class="card">
                <h3>💾 内存使用</h3>
                <div class="chart-container">
                    <canvas id="memoryChart"></canvas>
                </div>
            </div>
            
            <div class="card">
                <h3>📶 比特率</h3>
                <div class="chart-container">
                    <canvas id="bitrateChart"></canvas>
                </div>
            </div>
        </div>
        
        <div class="card">
            <h3>📝 实时日志</h3>
            <div id="logs" class="logs"></div>
        </div>
    </div>

    <script>
        // 初始化Socket.IO连接
        const socket = io();
        
        // 初始化图表
        const cpuChartCtx = document.getElementById('cpuChart').getContext('2d');
        const memoryChartCtx = document.getElementById('memoryChart').getContext('2d');
        const bitrateChartCtx = document.getElementById('bitrateChart').getContext('2d');
        
        const cpuChart = new Chart(cpuChartCtx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: 'CPU使用率 (%)',
                    data: [],
                    borderColor: 'rgb(75, 192, 192)',
                    tension: 0.1
                }]
            },
            options: { responsive: true, maintainAspectRatio: false }
        });
        
        const memoryChart = new Chart(memoryChartCtx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: '内存使用 (MB)',
                    data: [],
                    borderColor: 'rgb(255, 99, 132)',
                    tension: 0.1
                }]
            },
            options: { responsive: true, maintainAspectRatio: false }
        });
        
        const bitrateChart = new Chart(bitrateChartCtx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: '比特率 (kbps)',
                    data: [],
                    borderColor: 'rgb(54, 162, 235)',
                    tension: 0.1
                }]
            },
            options: { responsive: true, maintainAspectRatio: false }
        });
        
        // 连接事件
        socket.on('connect', function() {
            console.log('已连接到服务器');
            socket.emit('request_monitor_data');
        });
        
        // 接收监控更新
        socket.on('monitor_update', function(data) {
            updateCharts(data);
        });
        
        // 接收完整监控数据
        socket.on('monitor_data', function(data) {
            updateDashboard(data);
        });
        
        // 更新图表
        function updateCharts(data) {
            const now = new Date(data.timestamp).toLocaleTimeString();
            
            // 更新CPU图表
            cpuChart.data.labels.push(now);
            cpuChart.data.datasets[0].data.push(data.cpu_percent);
            if (cpuChart.data.labels.length > 20) {
                cpuChart.data.labels.shift();
                cpuChart.data.datasets[0].data.shift();
            }
            cpuChart.update();
            
            // 更新内存图表
            memoryChart.data.labels.push(now);
            memoryChart.data.datasets[0].data.push(data.memory_mb);
            if (memoryChart.data.labels.length > 20) {
                memoryChart.data.labels.shift();
                memoryChart.data.datasets[0].data.shift();
            }
            memoryChart.update();
        }
        
        // 更新仪表板
        function updateDashboard(data) {
            // 更新状态
            const statusEl = document.getElementById('streamStatus');
            if (data.status.main && data.status.main.is_running) {
                statusEl.innerHTML = `
                    <p><span class="status-indicator status-running"></span>主推流运行中</p>
                    <p>文件: ${data.status.main.current_file || '未知'}</p>
                    <p>PID: ${data.status.main.pid || '未知'}</p>
                    <p>运行时间: ${Math.floor(data.summary.uptime || 0)} 秒</p>
                `;
            } else {
                statusEl.innerHTML = '<p><span class="status-indicator status-stopped"></span>推流已停止</p>';
            }
            
            // 更新日志
            const logsEl = document.getElementById('logs');
            logsEl.innerHTML = '';
            data.logs.forEach(log => {
                const logEl = document.createElement('div');
                logEl.textContent = `[${new Date(log.timestamp).toLocaleTimeString()}] ${log.content}`;
                if (log.type === 'error') {
                    logEl.className = 'log-error';
                } else if (log.type === 'warning') {
                    logEl.className = 'log-warning';
                }
                logsEl.appendChild(logEl);
            });
            logsEl.scrollTop = logsEl.scrollHeight;
            
            // 更新比特率图表
            if (data.bitrates.length > 0) {
                const lastBitrate = data.bitrates[data.bitrates.length - 1];
                const time = new Date(lastBitrate.timestamp).toLocaleTimeString();
                bitrateChart.data.labels.push(time);
                bitrateChart.data.datasets[0].data.push(lastBitrate.bitrate);
                if (bitrateChart.data.labels.length > 20) {
                    bitrateChart.data.labels.shift();
                    bitrateChart.data.datasets[0].data.shift();
                }
                bitrateChart.update();
            }
        }
        
        // 控制函数
        function startMonitor() {
            fetch('/api/monitor/start', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    alert(data.msg);
                    if (data.code === 0) {
                        socket.emit('request_monitor_data');
                    }
                });
        }
        
        function stopMonitor() {
            fetch('/api/monitor/stop', { method: 'POST' })
                .then(response => response.json())
                .then(data => alert(data.msg));
        }
        
        function clearData() {
            fetch('/api/monitor/clear', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    alert(data.msg);
                    if (data.code === 0) {
                        // 清空图表
                        cpuChart.data.labels = [];
                        cpuChart.data.datasets[0].data = [];
                        cpuChart.update();
                        
                        memoryChart.data.labels = [];
                        memoryChart.data.datasets[0].data = [];
                        memoryChart.update();
                        
                        bitrateChart.data.labels = [];
                        bitrateChart.data.datasets[0].data = [];
                        bitrateChart.update();
                        
                        // 清空日志
                        document.getElementById('logs').innerHTML = '';
                    }
                });
        }
        
        // 定期请求数据更新
        setInterval(() => {
            socket.emit('request_monitor_data');
        }, 2000);
    </script>
</body>
</html>
'''

if __name__ == '__main__':
    import argparse
    parser = argparse.ArgumentParser(description='FFmpeg推流控制器')
    parser.add_argument('--debug', action='store_true', help='启用调试模式')
    parser.add_argument('--port', type=int, default=5000, help='端口号 (默认: 5000)')
    parser.add_argument('--host', default='0.0.0.0', help='主机地址 (默认: 0.0.0.0)')
    
    args = parser.parse_args()
    
    print("=" * 60)
    print("🎬 本地FFmpeg推流控制器")
    print("=" * 60)
    print(f"📡 服务地址: http://127.0.0.1:{args.port}")
    print(f"🌐 网络地址: http://{args.host}:{args.port}")
    print(f"📁 视频目录: {controller.video_dir}")
    print(f"📺 主推流地址: {controller.rtmp_url}")
    print(f"📺 备用推流地址: {controller.backup_rtmp_url}")
    print("=" * 60)
    
    try:
        if args.debug:
            print("⚠️  调试模式已启用（关闭自动重载以避免模块导入问题）")
            socketio.run(
                app,
                host=args.host, 
                port=args.port, 
                debug=True, 
                use_reloader=False  # 关闭自动重载
            )
        else:
            print("✅ 生产模式启动")
            socketio.run(
                app,
                host=args.host, 
                port=args.port, 
                debug=False
            )
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")
        controller.stop_push("all")  # 停止所有推流
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        controller.stop_push("all")  # 停止所有推流