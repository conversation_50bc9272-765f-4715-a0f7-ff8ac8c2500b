<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ZLMediaKit WebRTC播放器</title>
    <script src="http://localhost:8080/webrtc/ZLMRTCClient.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .video-container {
            text-align: center;
            margin: 20px 0;
        }
        video {
            width: 100%;
            max-width: 640px;
            height: 360px;
            background: #000;
            border-radius: 5px;
        }
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            font-size: 16px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            background-color: #007bff;
            color: white;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        .info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .error {
            color: red;
            background: #ffe6e6;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>ZLMediaKit WebRTC播放器</h1>
        
        <div class="info">
            <h3>当前流信息</h3>
            <p><strong>流地址:</strong> rtmp://localhost:1935/live/stream1</p>
            <p><strong>分辨率:</strong> 1920x1080</p>
            <p><strong>编码:</strong> H.264 + AAC</p>
            <p><strong>WebRTC地址:</strong> webrtc://localhost:8080/live/stream1</p>
        </div>

        <div class="video-container">
            <video id="videoPlayer" autoplay muted controls></video>
        </div>

        <div class="controls">
            <button id="playBtn" onclick="startPlay()">开始播放</button>
            <button id="stopBtn" onclick="stopPlay()" disabled>停止播放</button>
        </div>

        <div id="status" class="info">
            状态: 准备就绪
        </div>

        <div id="error" class="error" style="display: none;"></div>
    </div>

    <script>
        // 使用官方WebRTC API
        const video = document.getElementById('videoPlayer');
        const playBtn = document.getElementById('playBtn');
        const stopBtn = document.getElementById('stopBtn');
        const status = document.getElementById('status');
        const error = document.getElementById('error');
        
        let pc = null;
        let stream = null;

        function updateStatus(message) {
            status.textContent = `状态: ${message}`;
            console.log(message);
        }

        function showError(message) {
            error.textContent = `错误: ${message}`;
            error.style.display = 'block';
            console.error('WebRTC错误:', message);
            setTimeout(() => {
                error.style.display = 'none';
            }, 5000);
        }

        async function startPlay() {
            try {
                updateStatus('正在连接WebRTC...');
                
                playBtn.disabled = true;
                
                // 获取WebRTC SDP信息
                const response = await fetch('http://localhost:8080/index/api/webrtc?app=live&stream=stream1&type=play', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        type: 'play'
                    })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP错误! 状态: ${response.status}`);
                }
                
                const data = await response.json();
                
                if (data.code !== 0) {
                    throw new Error(data.msg || '获取SDP失败');
                }

                // 创建RTCPeerConnection
                const config = {
                    iceServers: []
                };
                
                pc = new RTCPeerConnection(config);
                
                // 设置远程描述
                await pc.setRemoteDescription({
                    type: 'offer',
                    sdp: data.sdp
                });
                
                // 创建应答
                const answer = await pc.createAnswer();
                await pc.setLocalDescription(answer);
                
                // 发送应答
                const answerResponse = await fetch('http://localhost:8080/index/api/webrtc?app=live&stream=stream1&type=play', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        type: 'answer',
                        sdp: answer.sdp
                    })
                });
                
                if (answerResponse.ok) {
                    const answerData = await answerResponse.json();
                    if (answerData.code === 0) {
                        updateStatus('WebRTC连接成功');
                        
                        // 获取媒体流
                        const remoteStream = new MediaStream();
                        pc.ontrack = (event) => {
                            remoteStream.addTrack(event.track);
                        };
                        
                        video.srcObject = remoteStream;
                        stopBtn.disabled = false;
                    } else {
                        throw new Error(answerData.msg || '发送应答失败');
                    }
                }
                
            } catch (err) {
                updateStatus('播放失败');
                showError(err.message);
                resetButtons();
                
                if (pc) {
                    pc.close();
                    pc = null;
                }
            }
        }

        function stopPlay() {
            if (pc) {
                pc.close();
                pc = null;
            }
            
            if (video.srcObject) {
                video.srcObject.getTracks().forEach(track => track.stop());
                video.srcObject = null;
            }
            
            updateStatus('播放已停止');
            resetButtons();
        }

        function resetButtons() {
            playBtn.disabled = false;
            stopBtn.disabled = true;
        }

        // 页面关闭时清理资源
        window.addEventListener('beforeunload', () => {
            stopPlay();
        });
    </script>
</body>
</html>