[tool:pytest]
# pytest配置文件

# 测试目录
testpaths = tests

# 测试文件模式
python_files = test_*.py *_test.py

# 测试类模式
python_classes = Test*

# 测试函数模式
python_functions = test_*

# 标记定义
markers =
    unit: 单元测试
    integration: 集成测试
    security: 安全测试
    performance: 性能测试
    slow: 慢速测试
    network: 需要网络的测试
    ffmpeg: 需要FFmpeg的测试

# 最小版本要求
minversion = 7.0

# 添加选项
addopts = 
    --strict-markers
    --strict-config
    --verbose
    --tb=short
    --cov=stream_controller
    --cov-report=html
    --cov-report=term-missing
    --cov-fail-under=80
    --durations=10

# 过滤警告
filterwarnings =
    ignore::UserWarning
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning

# 测试发现
norecursedirs = 
    .git
    .tox
    dist
    build
    *.egg
    __pycache__

# 日志配置
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# 并行测试
# 使用 pytest-xdist 进行并行测试
# 运行时使用: pytest -n auto
