---
name: 编译问题反馈
about: 反馈 ZLMediaKit 编译相关的问题
title: "[编译问题] 编译问题描述(必填)"
labels: 编译问题
assignees: ''

---

<!--
 请仔细阅读相关注释提示, 请务必根据提示填写相关信息.
 1. 信息不完整会影响问题的解决速度.
 1. 乱七八糟的渲染格式也会影响开发者心情, 同样会影响问题的解决. 提交前请务必点击 Preview/预览下反馈的显示效果.
 1. 不要删除模版内容, 模版的注释部分的内容不会显示，不需要删除，直接在各部分注释外面补充相关信息即可.
 -->

<!--
 markdown 语法参考:
 * https://docs.github.com/cn/get-started/writing-on-github/getting-started-with-writing-and-formatting-on-github/basic-writing-and-formatting-syntax
 * https://docs.github.com/en/get-started/writing-on-github/getting-started-with-writing-and-formatting-on-github/basic-writing-and-formatting-syntax
 -->

## 相关日志及环境信息

<!--
  由于编译日志通长较长, 建议将日志信息填写到下面 `````` block 内，或者上传日志文件
  -->

**清除编译缓存后，完整执行 cmake && make 命令的输出**

<details>
<summary>展开查看详细编译日志</summary>
<pre>

```
详细日志粘在这里!
```

</pre>
</details>

编译目录下的 `CMakeCache.txt` 文件内容，请直接上传为附件。

## 各种环境信息

<!--
  请填写相关环境信息, 详细的环境信息有助于快速复现定位问题.

  * 代码提交记录, 可使用命令 `git rev-parse HEAD` 进行查看.
  * 操作系统及版本, 如: Windows 10, CentOS 7, ...
  * 硬件信息, 如: Intel, AMD, ARM, 飞腾, 龙芯, ...
  -->

* **代码提交记录/git commit hash**:
* **操作系统及版本**:
* **硬件信息**:
* **其他需要补充的信息**:
